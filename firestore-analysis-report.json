{"timestamp": "2025-07-17T13:48:16.499Z", "summary": {"totalFiles": 25, "totalFindings": 191, "categories": {"imports": 0, "operations": 120, "storage": 25, "config": 46}}, "findings": {"backend/services/document-processor/src/utils/firebase.ts": [{"pattern": "firebase-admin\\/firestore", "count": 1, "lines": [{"lineNum": 2, "content": "import { getFirestore, FieldValue } from 'firebase-admin/firestore';"}]}, {"pattern": "getFirestore", "count": 2, "lines": [{"lineNum": 2, "content": "import { getFirestore, FieldValue } from 'firebase-admin/firestore';"}, {"lineNum": 59, "content": "this.db = getFirestore();"}]}, {"pattern": "firebase-admin\\/storage", "count": 1, "lines": [{"lineNum": 3, "content": "import { getStorage } from 'firebase-admin/storage';"}]}, {"pattern": "getStorage", "count": 2, "lines": [{"lineNum": 3, "content": "import { getStorage } from 'firebase-admin/storage';"}, {"lineNum": 60, "content": "this.storage = getStorage();"}]}, {"pattern": "firebase-admin\\/app", "count": 1, "lines": [{"lineNum": 1, "content": "import { initializeApp, getApps, cert } from 'firebase-admin/app';"}]}, {"pattern": "\\.collection\\(", "count": 13, "lines": [{"lineNum": 66, "content": "await this.db.collection('processing_logs').add({"}, {"lineNum": 77, "content": "await this.db.collection('audit_trail').add({"}, {"lineNum": 88, "content": "const batchRef = this.db.collection('batch_metadata').doc(batchId);"}, {"lineNum": 101, "content": "await this.db.collection('batch_metadata').doc(batch.batchId).set({"}, {"lineNum": 137, "content": "await this.db.collection('error_logs').add({"}, {"lineNum": 154, "content": "await this.db.collection('performance_logs').add({"}, {"lineNum": 169, "content": ".collection('processing_logs')"}, {"lineNum": 188, "content": "const doc = await this.db.collection('batch_metadata').doc(batchId).get();"}, {"lineNum": 202, "content": "await this.db.collection('job_status').doc(job.jobId).set({"}, {"lineNum": 213, "content": "const jobRef = this.db.collection('job_status').doc(jobId);"}, {"lineNum": 226, "content": "const doc = await this.db.collection('job_status').doc(jobId).get();"}, {"lineNum": 240, "content": ".collection('job_status')"}, {"lineNum": 259, "content": ".collection('job_status')"}]}, {"pattern": "\\.doc\\(", "count": 6, "lines": [{"lineNum": 88, "content": "const batchRef = this.db.collection('batch_metadata').doc(batchId);"}, {"lineNum": 101, "content": "await this.db.collection('batch_metadata').doc(batch.batchId).set({"}, {"lineNum": 188, "content": "const doc = await this.db.collection('batch_metadata').doc(batchId).get();"}, {"lineNum": 202, "content": "await this.db.collection('job_status').doc(job.jobId).set({"}, {"lineNum": 213, "content": "const jobRef = this.db.collection('job_status').doc(jobId);"}, {"lineNum": 226, "content": "const doc = await this.db.collection('job_status').doc(jobId).get();"}]}, {"pattern": "\\.add\\(", "count": 4, "lines": [{"lineNum": 66, "content": "await this.db.collection('processing_logs').add({"}, {"lineNum": 77, "content": "await this.db.collection('audit_trail').add({"}, {"lineNum": 137, "content": "await this.db.collection('error_logs').add({"}, {"lineNum": 154, "content": "await this.db.collection('performance_logs').add({"}]}, {"pattern": "\\.set\\(", "count": 2, "lines": [{"lineNum": 101, "content": "await this.db.collection('batch_metadata').doc(batch.batchId).set({"}, {"lineNum": 202, "content": "await this.db.collection('job_status').doc(job.jobId).set({"}]}, {"pattern": "\\.update\\(", "count": 2, "lines": [{"lineNum": 89, "content": "await batchRef.update({"}, {"lineNum": 214, "content": "await jobRef.update({"}]}, {"pattern": "\\.delete\\(", "count": 1, "lines": [{"lineNum": 265, "content": "batch.delete(doc.ref);"}]}, {"pattern": "FieldValue\\.", "count": 9, "lines": [{"lineNum": 68, "content": "timestamp: FieldValue.serverTimestamp(),"}, {"lineNum": 79, "content": "timestamp: FieldValue.serverTimestamp(),"}, {"lineNum": 92, "content": "updatedAt: FieldValue.serverTimestamp(),"}, {"lineNum": 103, "content": "createdAt: FieldValue.serverTimestamp(),"}, {"lineNum": 144, "content": "timestamp: FieldValue.serverTimestamp(),"}, {"lineNum": 158, "content": "timestamp: FieldValue.serverTimestamp(),"}, {"lineNum": 204, "content": "createdAt: FieldValue.serverTimestamp(),"}, {"lineNum": 217, "content": "updatedAt: FieldValue.serverTimestamp(),"}]}, {"pattern": "serverTimestamp", "count": 9, "lines": [{"lineNum": 68, "content": "timestamp: FieldValue.serverTimestamp(),"}, {"lineNum": 79, "content": "timestamp: FieldValue.serverTimestamp(),"}, {"lineNum": 92, "content": "updatedAt: FieldValue.serverTimestamp(),"}, {"lineNum": 103, "content": "createdAt: FieldValue.serverTimestamp(),"}, {"lineNum": 144, "content": "timestamp: FieldValue.serverTimestamp(),"}, {"lineNum": 158, "content": "timestamp: FieldValue.serverTimestamp(),"}, {"lineNum": 204, "content": "createdAt: FieldValue.serverTimestamp(),"}, {"lineNum": 217, "content": "updatedAt: FieldValue.serverTimestamp(),"}]}, {"pattern": "FIREBASE_", "count": 4, "lines": [{"lineNum": 35, "content": "const projectId = process.env.FIREBASE_PROJECT_ID;"}, {"lineNum": 37, "content": "const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n');"}]}], "backend/services/document-processor/src/utils/firebase.js": [{"pattern": "firebase-admin\\/firestore", "count": 1, "lines": [{"lineNum": 5, "content": "const firestore_1 = require(\"firebase-admin/firestore\");"}]}, {"pattern": "getFirestore", "count": 1, "lines": [{"lineNum": 30, "content": "this.db = (0, firestore_1.getFirestore)();"}]}, {"pattern": "firebase-admin\\/storage", "count": 1, "lines": [{"lineNum": 6, "content": "const storage_1 = require(\"firebase-admin/storage\");"}]}, {"pattern": "getStorage", "count": 1, "lines": [{"lineNum": 31, "content": "this.storage = (0, storage_1.getStorage)();"}]}, {"pattern": "firebase-admin\\/app", "count": 1, "lines": [{"lineNum": 4, "content": "const app_1 = require(\"firebase-admin/app\");"}]}, {"pattern": "\\.collection\\(", "count": 13, "lines": [{"lineNum": 36, "content": "await this.db.collection('processing_logs').add({"}, {"lineNum": 47, "content": "await this.db.collection('audit_trail').add({"}, {"lineNum": 58, "content": "const batchRef = this.db.collection('batch_metadata').doc(batchId);"}, {"lineNum": 71, "content": "await this.db.collection('batch_metadata').doc(batch.batchId).set({"}, {"lineNum": 105, "content": "await this.db.collection('error_logs').add({"}, {"lineNum": 122, "content": "await this.db.collection('performance_logs').add({"}, {"lineNum": 137, "content": ".collection('processing_logs')"}, {"lineNum": 155, "content": "const doc = await this.db.collection('batch_metadata').doc(batchId).get();"}, {"lineNum": 169, "content": "await this.db.collection('job_status').doc(job.jobId).set({"}, {"lineNum": 180, "content": "const jobRef = this.db.collection('job_status').doc(jobId);"}, {"lineNum": 193, "content": "const doc = await this.db.collection('job_status').doc(jobId).get();"}, {"lineNum": 207, "content": ".collection('job_status')"}, {"lineNum": 225, "content": ".collection('job_status')"}]}, {"pattern": "\\.doc\\(", "count": 6, "lines": [{"lineNum": 58, "content": "const batchRef = this.db.collection('batch_metadata').doc(batchId);"}, {"lineNum": 71, "content": "await this.db.collection('batch_metadata').doc(batch.batchId).set({"}, {"lineNum": 155, "content": "const doc = await this.db.collection('batch_metadata').doc(batchId).get();"}, {"lineNum": 169, "content": "await this.db.collection('job_status').doc(job.jobId).set({"}, {"lineNum": 180, "content": "const jobRef = this.db.collection('job_status').doc(jobId);"}, {"lineNum": 193, "content": "const doc = await this.db.collection('job_status').doc(jobId).get();"}]}, {"pattern": "\\.add\\(", "count": 4, "lines": [{"lineNum": 36, "content": "await this.db.collection('processing_logs').add({"}, {"lineNum": 47, "content": "await this.db.collection('audit_trail').add({"}, {"lineNum": 105, "content": "await this.db.collection('error_logs').add({"}, {"lineNum": 122, "content": "await this.db.collection('performance_logs').add({"}]}, {"pattern": "\\.set\\(", "count": 2, "lines": [{"lineNum": 71, "content": "await this.db.collection('batch_metadata').doc(batch.batchId).set({"}, {"lineNum": 169, "content": "await this.db.collection('job_status').doc(job.jobId).set({"}]}, {"pattern": "\\.update\\(", "count": 2, "lines": [{"lineNum": 59, "content": "await batchRef.update({"}, {"lineNum": 181, "content": "await jobRef.update({"}]}, {"pattern": "\\.delete\\(", "count": 1, "lines": [{"lineNum": 230, "content": "batch.delete(doc.ref);"}]}, {"pattern": "FieldValue\\.", "count": 9, "lines": [{"lineNum": 38, "content": "timestamp: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 49, "content": "timestamp: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 62, "content": "updatedAt: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 73, "content": "createdAt: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 112, "content": "timestamp: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 126, "content": "timestamp: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 171, "content": "createdAt: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 184, "content": "updatedAt: firestore_1.FieldValue.serverTimestamp(),"}]}, {"pattern": "serverTimestamp", "count": 9, "lines": [{"lineNum": 38, "content": "timestamp: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 49, "content": "timestamp: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 62, "content": "updatedAt: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 73, "content": "createdAt: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 112, "content": "timestamp: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 126, "content": "timestamp: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 171, "content": "createdAt: firestore_1.FieldValue.serverTimestamp(),"}, {"lineNum": 184, "content": "updatedAt: firestore_1.FieldValue.serverTimestamp(),"}]}, {"pattern": "FIREBASE_", "count": 4, "lines": [{"lineNum": 11, "content": "const projectId = process.env.FIREBASE_PROJECT_ID;"}, {"lineNum": 13, "content": "const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n');"}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/analyticsadmin.v1alpha.json": [{"pattern": "FIREBASE_", "count": 1, "lines": [{"lineNum": 9268, "content": "\"FIREBASE_LINK\","}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/analyticsadmin.v1beta.json": [{"pattern": "FIREBASE_", "count": 1, "lines": [{"lineNum": 3513, "content": "\"FIREBASE_LINK\","}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/firebaserules.v1.json": [{"pattern": "FIREBASE_", "count": 9, "lines": [{"lineNum": 242, "content": "\"description\": \"Optional. The requested runtime executable version. Defaults to FIREBASE_RULES_EXECUTABLE_V1.\","}, {"lineNum": 245, "content": "\"FIREBASE_RULES_EXECUTABLE_V1\","}, {"lineNum": 249, "content": "\"Executable format unspecified. Defaults to FIREBASE_RULES_EXECUTABLE_V1\","}, {"lineNum": 602, "content": "\"FIREBASE_RULES_EXECUTABLE_V1\","}, {"lineNum": 606, "content": "\"Executable format unspecified. Defaults to FIREBASE_RULES_EXECUTABLE_V1\","}, {"lineNum": 616, "content": "\"FIREBASE_RULES\","}, {"lineNum": 620, "content": "\"Language unspecified. Defaults to FIREBASE_RULES.\","}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/healthcare.v1.json": [{"pattern": "getStorage", "count": 4, "lines": [{"lineNum": 2345, "content": "\"getStorageInfo\": {"}, {"lineNum": 2347, "content": "\"flatPath\": \"v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}:getStorageInfo\","}, {"lineNum": 2349, "content": "\"id\": \"healthcare.projects.locations.datasets.dicomStores.dicomWeb.studies.series.instances.getStorageInfo\","}, {"lineNum": 2362, "content": "\"path\": \"v1/{+resource}:getStorageInfo\","}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/healthcare.v1beta1.json": [{"pattern": "getStorage", "count": 4, "lines": [{"lineNum": 2439, "content": "\"getStorageInfo\": {"}, {"lineNum": 2441, "content": "\"flatPath\": \"v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}:getStorageInfo\","}, {"lineNum": 2443, "content": "\"id\": \"healthcare.projects.locations.datasets.dicomStores.dicomWeb.studies.series.instances.getStorageInfo\","}, {"lineNum": 2456, "content": "\"path\": \"v1beta1/{+resource}:getStorageInfo\","}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/identitytoolkit.v2.json": [{"pattern": "FIREBASE_", "count": 2, "lines": [{"lineNum": 1884, "content": "\"FIREBASE_AUTH\""}, {"lineNum": 2360, "content": "\"FIREBASE_DYNAMIC_LINK_DOMAIN\","}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/searchads360.v0.json": [{"pattern": "FIREBASE_", "count": 6, "lines": [{"lineNum": 6927, "content": "\"FIREBASE_ANDROID_FIRST_OPEN\","}, {"lineNum": 6929, "content": "\"FIREBASE_ANDROID_CUSTOM\","}, {"lineNum": 6931, "content": "\"FIREBASE_IOS_IN_APP_PURCHASE\","}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/storage.v1.json": [{"pattern": "getStorage", "count": 2, "lines": [{"lineNum": 917, "content": "\"getStorageLayout\": {"}, {"lineNum": 920, "content": "\"id\": \"storage.buckets.getStorageLayout\","}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/urllib3/contrib/emscripten/emscripten_fetch_worker.js": [{"pattern": "\\.set\\(", "count": 4, "lines": [{"lineNum": 43, "content": "byteBuffer.set(errorBytes);"}, {"lineNum": 55, "content": "byteBuffer.set(value.subarray(curOffset, curOffset + curLen), 0);"}, {"lineNum": 84, "content": "byteBuffer.set(headerBytes);"}, {"lineNum": 103, "content": "byteBuffer.set(errorBytes);"}]}], "backend/services/pdf-processor/venv/lib/python3.11/site-packages/werkzeug/debug/shared/debugger.js": [{"pattern": "\\.add\\(", "count": 3, "lines": [{"lineNum": 211, "content": "consoleNode.classList.add(\"console\");"}, {"lineNum": 218, "content": "output.classList.add(\"output\");"}]}, {"pattern": "\\.set\\(", "count": 1, "lines": [{"lineNum": 42, "content": "params.set(\"s\", SECRET)"}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/analyticsadmin.v1alpha.json": [{"pattern": "FIREBASE_", "count": 1, "lines": [{"lineNum": 9268, "content": "\"FIREBASE_LINK\","}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/analyticsadmin.v1beta.json": [{"pattern": "FIREBASE_", "count": 1, "lines": [{"lineNum": 3513, "content": "\"FIREBASE_LINK\","}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/firebaserules.v1.json": [{"pattern": "FIREBASE_", "count": 9, "lines": [{"lineNum": 242, "content": "\"description\": \"Optional. The requested runtime executable version. Defaults to FIREBASE_RULES_EXECUTABLE_V1.\","}, {"lineNum": 245, "content": "\"FIREBASE_RULES_EXECUTABLE_V1\","}, {"lineNum": 249, "content": "\"Executable format unspecified. Defaults to FIREBASE_RULES_EXECUTABLE_V1\","}, {"lineNum": 602, "content": "\"FIREBASE_RULES_EXECUTABLE_V1\","}, {"lineNum": 606, "content": "\"Executable format unspecified. Defaults to FIREBASE_RULES_EXECUTABLE_V1\","}, {"lineNum": 616, "content": "\"FIREBASE_RULES\","}, {"lineNum": 620, "content": "\"Language unspecified. Defaults to FIREBASE_RULES.\","}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/healthcare.v1.json": [{"pattern": "getStorage", "count": 4, "lines": [{"lineNum": 2345, "content": "\"getStorageInfo\": {"}, {"lineNum": 2347, "content": "\"flatPath\": \"v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}:getStorageInfo\","}, {"lineNum": 2349, "content": "\"id\": \"healthcare.projects.locations.datasets.dicomStores.dicomWeb.studies.series.instances.getStorageInfo\","}, {"lineNum": 2362, "content": "\"path\": \"v1/{+resource}:getStorageInfo\","}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/healthcare.v1beta1.json": [{"pattern": "getStorage", "count": 4, "lines": [{"lineNum": 2439, "content": "\"getStorageInfo\": {"}, {"lineNum": 2441, "content": "\"flatPath\": \"v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}:getStorageInfo\","}, {"lineNum": 2443, "content": "\"id\": \"healthcare.projects.locations.datasets.dicomStores.dicomWeb.studies.series.instances.getStorageInfo\","}, {"lineNum": 2456, "content": "\"path\": \"v1beta1/{+resource}:getStorageInfo\","}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/identitytoolkit.v2.json": [{"pattern": "FIREBASE_", "count": 2, "lines": [{"lineNum": 1884, "content": "\"FIREBASE_AUTH\""}, {"lineNum": 2360, "content": "\"FIREBASE_DYNAMIC_LINK_DOMAIN\","}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/searchads360.v0.json": [{"pattern": "FIREBASE_", "count": 6, "lines": [{"lineNum": 6927, "content": "\"FIREBASE_ANDROID_FIRST_OPEN\","}, {"lineNum": 6929, "content": "\"FIREBASE_ANDROID_CUSTOM\","}, {"lineNum": 6931, "content": "\"FIREBASE_IOS_IN_APP_PURCHASE\","}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/storage.v1.json": [{"pattern": "getStorage", "count": 2, "lines": [{"lineNum": 917, "content": "\"getStorageLayout\": {"}, {"lineNum": 920, "content": "\"id\": \"storage.buckets.getStorageLayout\","}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/urllib3/contrib/emscripten/emscripten_fetch_worker.js": [{"pattern": "\\.set\\(", "count": 4, "lines": [{"lineNum": 43, "content": "byteBuffer.set(errorBytes);"}, {"lineNum": 55, "content": "byteBuffer.set(value.subarray(curOffset, curOffset + curLen), 0);"}, {"lineNum": 84, "content": "byteBuffer.set(headerBytes);"}, {"lineNum": 103, "content": "byteBuffer.set(errorBytes);"}]}], "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/werkzeug/debug/shared/debugger.js": [{"pattern": "\\.add\\(", "count": 3, "lines": [{"lineNum": 211, "content": "consoleNode.classList.add(\"console\");"}, {"lineNum": 218, "content": "output.classList.add(\"output\");"}]}, {"pattern": "\\.set\\(", "count": 1, "lines": [{"lineNum": 42, "content": "params.set(\"s\", SECRET)"}]}], "src/components/AccessibilityUtils.tsx": [{"pattern": "\\.add\\(", "count": 1, "lines": [{"lineNum": 154, "content": "document.body.classList.add('keyboard-navigation');"}]}], "src/components/TransactionsScreen.tsx": [{"pattern": "\\.add\\(", "count": 2, "lines": [{"lineNum": 99, "content": "months.add(`${monthKey}|${monthLabel}`);"}, {"lineNum": 102, "content": "categories.add(transaction.category);"}]}, {"pattern": "\\.set\\(", "count": 1, "lines": [{"lineNum": 289, "content": "url.searchParams.set('jobId', selectedJobId);"}]}], "backend/src/index.ts": [{"pattern": "\\.delete\\(", "count": 1, "lines": [{"lineNum": 87, "content": "apiRouter.delete('/transactions/:id', authenticateUser, TransactionsController.deleteTransaction);"}]}]}, "migrationTasks": [{"file": "backend/services/document-processor/src/utils/firebase.ts", "type": "database_operations", "priority": "HIGH", "description": "Migrate Firestore database operations to Supabase PostgreSQL", "actions": ["Replace Firestore collection operations with Supabase table operations", "Update data models to match PostgreSQL schema", "Replace FieldValue.serverTimestamp() with NOW()", "Update error handling for Supabase errors"]}, {"file": "backend/services/document-processor/src/utils/firebase.ts", "type": "imports_cleanup", "priority": "HIGH", "description": "Remove Firebase imports and replace with Supabase", "actions": ["Remove firebase-admin imports", "Add @supabase/supabase-js imports", "Update initialization code", "Remove Firebase environment variables"]}, {"file": "backend/services/document-processor/src/utils/firebase.js", "type": "database_operations", "priority": "HIGH", "description": "Migrate Firestore database operations to Supabase PostgreSQL", "actions": ["Replace Firestore collection operations with Supabase table operations", "Update data models to match PostgreSQL schema", "Replace FieldValue.serverTimestamp() with NOW()", "Update error handling for Supabase errors"]}, {"file": "backend/services/document-processor/src/utils/firebase.js", "type": "imports_cleanup", "priority": "HIGH", "description": "Remove Firebase imports and replace with Supabase", "actions": ["Remove firebase-admin imports", "Add @supabase/supabase-js imports", "Update initialization code", "Remove Firebase environment variables"]}, {"file": "backend/services/pdf-processor/venv/lib/python3.11/site-packages/urllib3/contrib/emscripten/emscripten_fetch_worker.js", "type": "database_operations", "priority": "HIGH", "description": "Migrate Firestore database operations to Supabase PostgreSQL", "actions": ["Replace Firestore collection operations with Supabase table operations", "Update data models to match PostgreSQL schema", "Replace FieldValue.serverTimestamp() with NOW()", "Update error handling for Supabase errors"]}, {"file": "backend/services/pdf-processor/venv/lib/python3.11/site-packages/werkzeug/debug/shared/debugger.js", "type": "database_operations", "priority": "HIGH", "description": "Migrate Firestore database operations to Supabase PostgreSQL", "actions": ["Replace Firestore collection operations with Supabase table operations", "Update data models to match PostgreSQL schema", "Replace FieldValue.serverTimestamp() with NOW()", "Update error handling for Supabase errors"]}, {"file": "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/urllib3/contrib/emscripten/emscripten_fetch_worker.js", "type": "database_operations", "priority": "HIGH", "description": "Migrate Firestore database operations to Supabase PostgreSQL", "actions": ["Replace Firestore collection operations with Supabase table operations", "Update data models to match PostgreSQL schema", "Replace FieldValue.serverTimestamp() with NOW()", "Update error handling for Supabase errors"]}, {"file": "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/werkzeug/debug/shared/debugger.js", "type": "database_operations", "priority": "HIGH", "description": "Migrate Firestore database operations to Supabase PostgreSQL", "actions": ["Replace Firestore collection operations with Supabase table operations", "Update data models to match PostgreSQL schema", "Replace FieldValue.serverTimestamp() with NOW()", "Update error handling for Supabase errors"]}, {"file": "src/components/AccessibilityUtils.tsx", "type": "database_operations", "priority": "HIGH", "description": "Migrate Firestore database operations to Supabase PostgreSQL", "actions": ["Replace Firestore collection operations with Supabase table operations", "Update data models to match PostgreSQL schema", "Replace FieldValue.serverTimestamp() with NOW()", "Update error handling for Supabase errors"]}, {"file": "src/components/TransactionsScreen.tsx", "type": "database_operations", "priority": "HIGH", "description": "Migrate Firestore database operations to Supabase PostgreSQL", "actions": ["Replace Firestore collection operations with Supabase table operations", "Update data models to match PostgreSQL schema", "Replace FieldValue.serverTimestamp() with NOW()", "Update error handling for Supabase errors"]}, {"file": "backend/services/document-processor/src/utils/firebase.ts", "type": "storage_operations", "priority": "MEDIUM", "description": "Migrate Firebase Storage to Supabase Storage", "actions": ["Replace Firebase Storage with Supabase Storage API", "Update file upload/download logic", "Migrate existing files to Supabase Storage", "Update access control policies"]}, {"file": "backend/services/document-processor/src/utils/firebase.js", "type": "storage_operations", "priority": "MEDIUM", "description": "Migrate Firebase Storage to Supabase Storage", "actions": ["Replace Firebase Storage with Supabase Storage API", "Update file upload/download logic", "Migrate existing files to Supabase Storage", "Update access control policies"]}, {"file": "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/healthcare.v1.json", "type": "storage_operations", "priority": "MEDIUM", "description": "Migrate Firebase Storage to Supabase Storage", "actions": ["Replace Firebase Storage with Supabase Storage API", "Update file upload/download logic", "Migrate existing files to Supabase Storage", "Update access control policies"]}, {"file": "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/healthcare.v1beta1.json", "type": "storage_operations", "priority": "MEDIUM", "description": "Migrate Firebase Storage to Supabase Storage", "actions": ["Replace Firebase Storage with Supabase Storage API", "Update file upload/download logic", "Migrate existing files to Supabase Storage", "Update access control policies"]}, {"file": "backend/services/pdf-processor/venv/lib/python3.11/site-packages/googleapiclient/discovery_cache/documents/storage.v1.json", "type": "storage_operations", "priority": "MEDIUM", "description": "Migrate Firebase Storage to Supabase Storage", "actions": ["Replace Firebase Storage with Supabase Storage API", "Update file upload/download logic", "Migrate existing files to Supabase Storage", "Update access control policies"]}, {"file": "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/healthcare.v1.json", "type": "storage_operations", "priority": "MEDIUM", "description": "Migrate Firebase Storage to Supabase Storage", "actions": ["Replace Firebase Storage with Supabase Storage API", "Update file upload/download logic", "Migrate existing files to Supabase Storage", "Update access control policies"]}, {"file": "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/healthcare.v1beta1.json", "type": "storage_operations", "priority": "MEDIUM", "description": "Migrate Firebase Storage to Supabase Storage", "actions": ["Replace Firebase Storage with Supabase Storage API", "Update file upload/download logic", "Migrate existing files to Supabase Storage", "Update access control policies"]}, {"file": "backend/services/pdf-processor/venv/lib64/python3.11/site-packages/googleapiclient/discovery_cache/documents/storage.v1.json", "type": "storage_operations", "priority": "MEDIUM", "description": "Migrate Firebase Storage to Supabase Storage", "actions": ["Replace Firebase Storage with Supabase Storage API", "Update file upload/download logic", "Migrate existing files to Supabase Storage", "Update access control policies"]}]}