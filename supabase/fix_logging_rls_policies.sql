-- ===================================================================
-- FIX SUPABASE RLS POLICIES FOR LOGGING TABLES
-- Run this script in your Supabase SQL Editor to fix 403 Forbidden errors
-- ===================================================================

-- Fix processing_logs: Add INSERT/UPDATE policies for authenticated users
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'processing_logs' AND policyname = 'Users can insert their own processing logs') THEN
        CREATE POLICY "Users can insert their own processing logs" ON processing_logs
            FOR INSERT WITH CHECK (auth.uid()::text = user_id);
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'processing_logs' AND policyname = 'Users can update their own processing logs') THEN
        CREATE POLICY "Users can update their own processing logs" ON processing_logs
            FOR UPDATE USING (auth.uid()::text = user_id);
    END IF;
END $$;

-- Fix error_logs: Add policies for authenticated users
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'error_logs' AND policyname = 'Users can view their own error logs') THEN
        CREATE POLICY "Users can view their own error logs" ON error_logs
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'error_logs' AND policyname = 'Users can insert their own error logs') THEN
        CREATE POLICY "Users can insert their own error logs" ON error_logs
            FOR INSERT WITH CHECK (auth.uid()::text = user_id);
    END IF;
END $$;

-- Fix performance_logs: Add policies for authenticated users
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'performance_logs' AND policyname = 'Users can view their own performance logs') THEN
        CREATE POLICY "Users can view their own performance logs" ON performance_logs
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'performance_logs' AND policyname = 'Users can insert their own performance logs') THEN
        CREATE POLICY "Users can insert their own performance logs" ON performance_logs
            FOR INSERT WITH CHECK (auth.uid()::text = user_id);
    END IF;
END $$;

-- Verify policies were created
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename IN ('processing_logs', 'error_logs', 'performance_logs')
ORDER BY tablename, policyname;
