# FinScope Environment Variables Configuration

This document outlines the environment variables required for FinScope after the Supabase migration (Phase 1 complete).

## 🔄 Migration Status: Phase 1 Complete

✅ **Authentication**: Migrated from Firebase Auth to Supabase Auth  
✅ **Backend Services**: Updated to use Supabase JWT verification  
✅ **Frontend**: Updated to use Supabase Auth SDK  
✅ **Document Processor**: Migrated logging and job management to Supabase  

## 📋 Required Environment Variables

### Frontend (.env)
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://iaqfusmumkdifdrctzma.supabase.co
VITE_SUPABASE_PUBLISHABLE_KEY=sb_publishable_your-key-here

# Google AI
VITE_GEMINI_API_KEY=your-gemini-api-key

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api/v1
```

### Main Backend (.env)
```env
# Server Configuration
PORT=3001
HOST=0.0.0.0
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Supabase Configuration
SUPABASE_URL=https://iaqfusmumkdifdrctzma.supabase.co
SUPABASE_PUBLISHABLE_KEY=sb_publishable_your-key-here
SUPABASE_SECRET_KEY=sb_secret_your-key-here

# Google AI
GEMINI_API_KEY=your-gemini-api-key

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/jpg,image/png,image/webp,application/pdf

# Redis Configuration (for job queues)
UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# Job Queue Configuration
QUEUE_CONCURRENCY=2
QUEUE_RETRY_ATTEMPTS=3
QUEUE_RETRY_DELAY=2000

# Processing Configuration
MAX_FILE_SIZE=10485760
ALLOWED_MIME_TYPES=image/png,image/jpeg,image/jpg,application/pdf,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel

# Logging Configuration
LOG_LEVEL=info
ENABLE_AUDIT_TRAIL=true
ENABLE_PERFORMANCE_LOGGING=true
```

### Document Processor Service (.env)
```env
# Server Configuration
PORT=3002
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=https://iaqfusmumkdifdrctzma.supabase.co
SUPABASE_PUBLISHABLE_KEY=sb_publishable_your-key-here
SUPABASE_SECRET_KEY=sb_secret_your-key-here

# Google AI
GEMINI_API_KEY=your-gemini-api-key

# Redis Configuration
UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# PDF Service Configuration
PDF_SERVICE_URL=http://localhost:8080

# Processing Configuration
MAX_FILE_SIZE=10485760
MAX_BATCH_SIZE=10
ENABLE_LOGGING=true
ENABLE_AUDIT_TRAIL=true
```

### PDF Processor Service (.env)
```env
# Server Configuration
PORT=8080
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=https://iaqfusmumkdifdrctzma.supabase.co
SUPABASE_PUBLISHABLE_KEY=sb_publishable_your-key-here
SUPABASE_SECRET_KEY=sb_secret_your-key-here

# Processing Configuration
MAX_FILE_SIZE=10485760
MAX_BATCH_SIZE=10
PDF_DPI=300
TEXT_EXTRACTION_THRESHOLD=100

# Logging Configuration
LOG_LEVEL=INFO
ENABLE_SUPABASE_LOGGING=true
```

## 🗑️ Removed Variables (Firebase Auth)

The following Firebase Auth variables are **NO LONGER NEEDED** after Phase 1 migration:

```env
# ❌ REMOVED - No longer needed
FIREBASE_API_KEY
FIREBASE_AUTH_DOMAIN
FIREBASE_PROJECT_ID
FIREBASE_STORAGE_BUCKET
FIREBASE_MESSAGING_SENDER_ID
FIREBASE_APP_ID
FIREBASE_MEASUREMENT_ID
FIREBASE_CLIENT_EMAIL
FIREBASE_PRIVATE_KEY
```

## 🔧 Setup Instructions

### 1. Supabase Configuration

1. **Get Supabase Credentials**:
   - Go to [Supabase Dashboard](https://supabase.com/dashboard)
   - Select your project: `finscope-kaycee`
   - Go to Settings → API
   - Copy the Project URL and API Keys

2. **Required Supabase Keys**:
   - `SUPABASE_URL`: Your project URL
   - `VITE_SUPABASE_PUBLISHABLE_KEY`: Publishable key (for frontend)
   - `SUPABASE_PUBLISHABLE_KEY`: Publishable key (for backend, if needed)
   - `SUPABASE_SECRET_KEY`: Secret key (for backend)

### 2. Database Setup

Run the migration SQL to create necessary tables:
```bash
# Apply the Supabase migration
psql -h your-supabase-host -U postgres -d postgres -f supabase-migration-tables.sql
```

### 3. Authentication Setup

1. **Enable Auth Providers** in Supabase:
   - Go to Authentication → Providers
   - Enable Google OAuth
   - Configure redirect URLs

2. **Update Frontend URLs**:
   - Add your domain to allowed redirect URLs
   - Configure site URL in Supabase Auth settings

## 🔍 Verification

### Test Authentication
```bash
# Test frontend auth
curl -X POST http://localhost:5173/auth/test

# Test backend auth
curl -H "Authorization: Bearer your-token" http://localhost:3001/api/v1/transactions
```

### Test Services
```bash
# Test document processor
curl http://localhost:3002/health

# Test PDF processor
curl http://localhost:8080/health
```

## 🚨 Security Notes

1. **Never commit real environment variables** to version control
2. **Use different keys** for development and production
3. **Rotate service role keys** regularly
4. **Enable Row Level Security (RLS)** on all Supabase tables
5. **Use HTTPS** in production for all API endpoints

## 📚 Next Steps

After Phase 1 completion, the following phases are planned:

- **Phase 2**: Complete database migration (remaining Firestore collections)
- **Phase 3**: Real-time features migration
- **Phase 4**: Storage and logging migration

## 🆘 Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify Supabase keys are correct
   - Check if JWT tokens are properly formatted
   - Ensure RLS policies allow access

2. **Database Connection Issues**:
   - Verify SUPABASE_URL is correct
   - Check if service role key has proper permissions
   - Ensure database tables exist

3. **CORS Issues**:
   - Add your frontend URL to CORS configuration
   - Check Supabase Auth redirect URLs

### Debug Commands

```bash
# Check environment variables
env | grep SUPABASE

# Test Supabase connection
node -e "const { createClient } = require('@supabase/supabase-js'); const client = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY); console.log('Connected:', !!client);"

# Verify database tables
psql -h your-host -U postgres -c "\dt"
```
