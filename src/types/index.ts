export interface Transaction {
  id: string;
  user_id: string;
  fingerprint?: string;
  date: string;
  time: string | null;
  description: string;
  amount: number;
  type: 'credit' | 'debit';
  category?: string;
  vendor?: string;
  notes?: string;
  bank_provided_id: string | null;
  source_bank: string;
  
  // Enhanced optional fields
  sender_name?: string;
  receiver_name?: string;
  sender_account_number?: string;
  receiver_account_number?: string;
  account_type?: string;
  customer_id?: string;
  transaction_fee?: number;
  currency_type?: string;
  exchange_rate?: number;
  transaction_status?: string;
  payment_method?: string;
  narration?: string;
  invoice_number?: string;
  merchant_name?: string;
  authorization_code?: string;
  destination_bank?: string;
  bank_branch?: string;
  payment_gateway?: string;
  terminal_id?: string;
  channel?: string;
  session_id?: string;
  device_id?: string;
  ip_address?: string;
  geolocation?: string;
  balance_before?: number;
  balance_after?: number;
  
  created_at?: string;
  updated_at?: string;
}

export interface AnalysisResponse {
  success: boolean;
  transactions: Transaction[];
  detectedBank: string;
  message?: string;
  error?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface TransactionStats {
  totalIncome: number;
  totalExpenses: number;
  netCashFlow: number;
  transactionCount: number;
  categoryBreakdown: Record<string, number>;
}

export interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
}

export type ScreenType = 'dashboard' | 'upload' | 'transactions' | 'insights' | 'settings';

// New types for migrated database schema
export interface JobData {
  id: string;
  job_id: string;
  user_id: string;
  file_name: string;
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  file_size: number;
  mime_type: string;
  progress: number;
  error?: string;
  result?: {
    transactionCount: number;
    processingTime: number;
    confidence: number;
  };
  options?: Record<string, any>;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  failed_at?: string;
  cancelled_at?: string;
  updated_at: string;
}

export interface ProcessingLog {
  id: string;
  user_id: string;
  operation: string;
  status: string;
  duration_ms?: number;
  file_name?: string;
  file_size?: number;
  mime_type?: string;
  transaction_count?: number;
  confidence_score?: number;
  error_message?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  created_at: string;
}

export interface AuditTrail {
  id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  timestamp: string;
  created_at: string;
}

export interface BatchMetadata {
  id: string;
  batch_id: string;
  user_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  file_count: number;
  processed_count: number;
  failed_count: number;
  total_transactions: number;
  processing_time_ms?: number;
  confidence_score?: number;
  error_message?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface ErrorLog {
  id: string;
  error_name: string;
  error_message: string;
  error_stack?: string;
  context?: Record<string, any>;
  user_id?: string;
  request_id?: string;
  timestamp: string;
  created_at: string;
}

export interface PerformanceLog {
  id: string;
  operation: string;
  duration: number;
  metadata?: Record<string, any>;
  user_id?: string;
  timestamp: string;
  created_at: string;
}