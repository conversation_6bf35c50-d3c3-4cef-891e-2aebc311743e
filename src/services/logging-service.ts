/**
 * Enhanced Logging Service
 * 
 * This service provides comprehensive logging functionality using the
 * error_logs and performance_logs tables created in Phase 2.
 */

import { supabase } from '../../supabaseClient';
import { ErrorLog, PerformanceLog, ProcessingLog } from '../types';
import { apiService } from './api';

export interface LogContext {
  userId?: string;
  requestId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, any>;
}

export interface PerformanceMetrics {
  operation: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
  userId?: string;
}

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'critical';

class LoggingService {
  private performanceMetrics: Map<string, PerformanceMetrics> = new Map();
  private sessionId: string;
  private userId?: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandling();
  }

  /**
   * Set current user ID for logging context
   */
  setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Setup global error handling
   */
  private setupGlobalErrorHandling(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError('UnhandledPromiseRejection', event.reason?.message || 'Unhandled promise rejection', {
      });
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      this.logError('GlobalError', event.message, {
      });
    });
  }

  /**
   * Log error to database
   */
  async logError(
    errorName: string,
    errorMessage: string,
    context?: LogContext,
    errorStack?: string
  ): Promise<void> {
    try {
      const errorData: Omit<ErrorLog, 'id' | 'created_at'> = {
        error_name: errorName,
        error_message: errorMessage,
        error_stack: errorStack,
        context: {
          ...context,
          sessionId: this.sessionId,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href
        },
        user_id: context?.userId || this.userId,
        request_id: context?.requestId,
        timestamp: new Date().toISOString()
      };

      // Use API service instead of direct Supabase call
      await apiService.createErrorLog(errorData);

      // Also log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`[${errorName}] ${errorMessage}`, context);
      }
    } catch (error) {
      console.error('Failed to log error to database:', error);
    }
  }

  /**
   * Log performance metrics
   */
  async logPerformance(
    operation: string,
    duration: number,
    metadata?: Record<string, any>,
    userId?: string
  ): Promise<void> {
    try {
      const perfData: Omit<PerformanceLog, 'id' | 'created_at'> = {
        operation,
        duration,
        metadata: {
          ...metadata,
          sessionId: this.sessionId,
          userAgent: navigator.userAgent,
          url: window.location.href
        },
        user_id: userId || this.userId,
        timestamp: new Date().toISOString()
      };

      // Use API service instead of direct Supabase call
      await apiService.createPerformanceLog(perfData);

      // Log slow operations to console
      if (duration > 1000) {
        console.warn(`Slow operation detected: ${operation} took ${duration}ms`);
      }
    } catch (error) {
      console.error('Failed to log performance to database:', error);
    }
  }

  /**
   * Log processing operation
   */
  async logProcessing(
    operation: string,
    status: string,
    metadata?: Record<string, any>,
    userId?: string
  ): Promise<void> {
    try {
      const processingData: Omit<ProcessingLog, 'id' | 'created_at'> = {
        user_id: userId || this.userId || 'anonymous',
        operation,
        status,
        duration_ms: metadata?.duration_ms,
        file_name: metadata?.file_name,
        file_size: metadata?.file_size,
        mime_type: metadata?.mime_type,
        transaction_count: metadata?.transaction_count,
        confidence_score: metadata?.confidence_score,
        error_message: metadata?.error_message,
        metadata: {
          ...metadata,
          sessionId: this.sessionId
        },
        timestamp: new Date().toISOString()
      };

      await supabase
        .from('processing_logs')
        .insert([processingData]);
    } catch (error) {
      console.error('Failed to log processing to database:', error);
    }
  }

  /**
   * Start performance tracking
   */
  startPerformanceTracking(operation: string, metadata?: Record<string, any>): string {
    const trackingId = `${operation}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.performanceMetrics.set(trackingId, {
      operation,
      startTime: performance.now(),
      metadata,
      userId: this.userId
    });

    return trackingId;
  }

  /**
   * End performance tracking and log results
   */
  async endPerformanceTracking(trackingId: string, additionalMetadata?: Record<string, any>): Promise<void> {
    const metrics = this.performanceMetrics.get(trackingId);
    if (!metrics) {
      console.warn(`Performance tracking ID not found: ${trackingId}`);
      return;
    }

    const endTime = performance.now();
    const duration = endTime - metrics.startTime;

    await this.logPerformance(
      metrics.operation,
      duration,
      {
        ...metrics.metadata,
        ...additionalMetadata
      },
      metrics.userId
    );

    this.performanceMetrics.delete(trackingId);
  }

  /**
   * Log API call performance
   */
  async logApiCall(
    endpoint: string,
    method: string,
    duration: number,
    status: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logPerformance(
      `api_call_${method.toLowerCase()}_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
      duration,
      {
        endpoint,
        method,
        status,
        ...metadata
      }
    );
  }

  /**
   * Log user action
   */
  async logUserAction(
    action: string,
    component: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logProcessing(
      `user_action_${action}`,
      'completed',
      {
        component,
        action,
        ...metadata
      }
    );
  }

  /**
   * Log file upload
   */
  async logFileUpload(
    fileName: string,
    fileSize: number,
    mimeType: string,
    duration: number,
    success: boolean,
    errorMessage?: string
  ): Promise<void> {
    await this.logProcessing(
      'file_upload',
      success ? 'completed' : 'failed',
      {
        file_name: fileName,
        file_size: fileSize,
        mime_type: mimeType,
        duration_ms: duration,
        error_message: errorMessage
      }
    );
  }

  /**
   * Log document processing
   */
  async logDocumentProcessing(
    fileName: string,
    processingType: string,
    duration: number,
    transactionCount?: number,
    confidence?: number,
    success: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    await this.logProcessing(
      `document_processing_${processingType}`,
      success ? 'completed' : 'failed',
      {
        file_name: fileName,
        duration_ms: duration,
        transaction_count: transactionCount,
        confidence_score: confidence,
        error_message: errorMessage
      }
    );
  }

  /**
   * Create error logger for specific component
   */
  createComponentLogger(componentName: string) {
    return {
      error: (message: string, error?: Error, metadata?: Record<string, any>) => {
        this.logError(
          `${componentName}Error`,
          message,
          {
            component: componentName,
            ...metadata
          },
          error?.stack
        );
      },
      performance: (operation: string, duration: number, metadata?: Record<string, any>) => {
        this.logPerformance(
          `${componentName}_${operation}`,
          duration,
          {
            component: componentName,
            ...metadata
          }
        );
      },
      action: (action: string, metadata?: Record<string, any>) => {
        this.logUserAction(action, componentName, metadata);
      }
    };
  }

  /**
   * Get recent error logs for debugging
   */
  async getRecentErrors(limit: number = 50): Promise<ErrorLog[]> {
    try {
      const { data, error } = await supabase
        .from('error_logs')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to fetch error logs:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch error logs:', error);
      return [];
    }
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(operation?: string, limit: number = 100): Promise<PerformanceLog[]> {
    try {
      let query = supabase
        .from('performance_logs')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (operation) {
        query = query.eq('operation', operation);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Failed to fetch performance logs:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch performance logs:', error);
      return [];
    }
  }
}

// Export singleton instance
export const loggingService = new LoggingService();
export default loggingService;
