/* FinScope Design System - CSS Variables */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
:root {
  /* Primary Colors */
  --primary-green: #2E8B57;
  --primary-green-dark: #257348;
  --primary-green-light-tint: #EAF4F0;
  --primary-green-extra-light-tint: #F0FFF8;
  --dark-charcoal-green: #2F4F4F;

  /* Text Colors */
  --text-color-primary: #343A40;
  --text-color-secondary: #6C757D;
  --text-color-on-primary: #FFFFFF;

  /* Background Colors */
  --card-bg: #FFFFFF;
  --light-tint-grey: #F8F9FA;

  /* Border Colors */
  --border-color: #DEE2E6;
  --border-color-lighter: #E9ECEF;

  /* Semantic Colors */
  --semantic-credit: #28A745;
  --semantic-debit: #DC3545;
  --semantic-error: #DC3545;
  --semantic-error-bg: #F8D7DA;
  --accent-blue: #007BFF;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --font-size-base: 16px;
  --font-size-hero: 28px;
  --font-size-title: 24px;
  --font-size-card-header: 18px;
  --font-size-body: 16px;
  --font-size-small: 14px;

  /* Font Weights */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing */
  --border-radius: 12px;
  --border-radius-small: 8px;
  --header-height: 70px;
  --bottom-nav-height: 60px;

  /* Shadows */
  --card-shadow: 0 4px 12px rgba(0,0,0,0.08);
  --button-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  color: var(--text-color-primary);
  background-color: var(--light-tint-grey);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
}

/* Typography Hierarchy */
.hero-amount {
  font-size: var(--font-size-hero);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
}

.screen-title {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  color: var(--dark-charcoal-green);
  line-height: 1.3;
}

.card-header {
  font-size: var(--font-size-card-header);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  line-height: 1.4;
}

.body-text {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-regular);
  color: var(--text-color-primary);
  line-height: 1.5;
}

.body-text-medium {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  line-height: 1.5;
}

.small-text {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-regular);
  color: var(--text-color-secondary);
  line-height: 1.4;
}

.small-text-medium {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-secondary);
  line-height: 1.4;
}

/* Semantic Text Colors */
.text-credit {
  color: var(--semantic-credit);
}

.text-debit {
  color: var(--semantic-debit);
}

.text-error {
  color: var(--semantic-error);
}

.text-primary {
  color: var(--text-color-primary);
}

.text-secondary {
  color: var(--text-color-secondary);
}

.text-on-primary {
  color: var(--text-color-on-primary);
}

/* Icon System */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}

.icon svg {
  display: block;
  stroke: currentColor;
  fill: none;
}

/* Icon size variants */
.icon-small {
  width: 18px;
  height: 18px;
}

.icon-medium {
  width: 20px;
  height: 20px;
}

.icon-large {
  width: 24px;
  height: 24px;
}

/* Base Component Classes */

/* Card Component */
.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 24px;
  border: none;
}

.card-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color-lighter);
}

.card-body {
  margin-bottom: 16px;
}

.card-footer {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid var(--border-color-lighter);
}

/* Button Component */
.action-button {
  border: none;
  border-radius: var(--border-radius-small);
  padding: 12px 20px;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family);
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  box-shadow: var(--button-shadow);
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button Variants */
.action-button.primary {
  background-color: var(--primary-green);
  color: var(--text-color-on-primary);
}

.action-button.primary:hover:not(:disabled) {
  background-color: var(--primary-green-dark);
}

.action-button.secondary {
  background-color: var(--text-color-secondary);
  color: var(--text-color-on-primary);
}

.action-button.secondary:hover:not(:disabled) {
  background-color: var(--text-color-primary);
}

.action-button.danger {
  background-color: var(--semantic-error);
  color: var(--text-color-on-primary);
}

.action-button.danger:hover:not(:disabled) {
  background-color: #c82333;
}

.action-button.outline {
  background-color: transparent;
  color: var(--primary-green);
  border: 1px solid var(--primary-green);
  box-shadow: none;
}

.action-button.outline:hover:not(:disabled) {
  background-color: var(--primary-green-light-tint);
}

/* Input Components */
.form-input, .form-select, .form-textarea {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  padding: 10px 12px;
  font-size: var(--font-size-body);
  font-family: var(--font-family);
  color: var(--text-color-primary);
  background-color: var(--card-bg);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  width: 100%;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px var(--primary-green-light-tint);
}

.form-input::placeholder, .form-textarea::placeholder {
  color: var(--text-color-secondary);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Modal Components */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid var(--border-color-lighter);
  margin-bottom: 24px;
}

.modal-body {
  padding: 0 24px 24px 24px;
}

.modal-footer {
  padding: 0 24px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* Card Variants and Enhancements */
.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.12);
  transition: all 0.2s ease;
}

.card-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.1);
}

/* Stat Card */
.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.stat-card-icon {
  margin-bottom: 8px;
  color: var(--primary-green);
}

.stat-card-title {
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card-value {
  margin: 8px 0;
  color: var(--text-color-primary);
}

.stat-card-subtitle {
  margin: 0;
}

.stat-card-up .stat-card-value {
  color: var(--semantic-credit);
}

.stat-card-down .stat-card-value {
  color: var(--semantic-debit);
}

/* Insight Card */
.insight-card {
  padding: 20px;
}

.insight-card-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.insight-card-icon {
  color: var(--primary-green);
  flex-shrink: 0;
}

.insight-card-info {
  flex: 1;
}

.insight-card-title {
  margin: 0 0 8px 0;
}

.insight-card-description {
  margin: 0;
}

/* Button Enhancements */
.action-button.small {
  padding: 8px 16px;
  font-size: var(--font-size-small);
}

.action-button.large {
  padding: 16px 24px;
  font-size: var(--font-size-card-header);
}

.action-button.full-width {
  width: 100%;
}

.action-button.loading {
  position: relative;
  pointer-events: none;
}

.button-icon {
  flex-shrink: 0;
}

.button-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Icon Button */
.icon-button {
  border: none;
  border-radius: var(--border-radius-small);
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.icon-button.small {
  padding: 6px;
}

.icon-button.large {
  padding: 12px;
}

.icon-button.ghost {
  color: var(--text-color-secondary);
}

.icon-button.ghost:hover:not(:disabled) {
  background-color: var(--primary-green-light-tint);
  color: var(--primary-green);
}

.icon-button.primary {
  background-color: var(--primary-green);
  color: var(--text-color-on-primary);
}

.icon-button.primary:hover:not(:disabled) {
  background-color: var(--primary-green-dark);
}

.icon-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Floating Action Button */
.fab {
  position: fixed;
  bottom: calc(var(--bottom-nav-height) + 20px);
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  background-color: var(--primary-green);
  color: var(--text-color-on-primary);
  box-shadow: 0 4px 12px rgba(46, 139, 87, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
}

.fab:hover {
  background-color: var(--primary-green-dark);
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(46, 139, 87, 0.4);
}

/* Input Component Enhancements */
.input-group {
  margin-bottom: 16px;
}

.input-label {
  display: block;
  margin-bottom: 6px;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.input-label .required {
  color: var(--semantic-error);
  margin-left: 2px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper .input-icon {
  position: absolute;
  color: var(--text-color-secondary);
  pointer-events: none;
  z-index: 1;
}

.input-wrapper .input-icon.left {
  left: 12px;
}

.input-wrapper .input-icon.right {
  right: 12px;
}

.form-input.has-icon-left {
  padding-left: 40px;
}

.form-input.has-icon-right {
  padding-right: 40px;
}

.form-input.error, .form-select.error, .form-textarea.error {
  border-color: var(--semantic-error);
}

.form-input.error:focus, .form-select.error:focus, .form-textarea.error:focus {
  border-color: var(--semantic-error);
  box-shadow: 0 0 0 3px var(--semantic-error-bg);
}

.input-error {
  display: block;
  margin-top: 4px;
  font-size: var(--font-size-small);
  color: var(--semantic-error);
}

.full-width {
  width: 100%;
}

/* Select Component */
.select-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.select-wrapper .select-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  pointer-events: none;
}

.form-select {
  appearance: none;
  background-image: none;
  padding-right: 40px;
  cursor: pointer;
}

.form-select::-ms-expand {
  display: none;
}

/* Modal Component Enhancements */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal-content {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-small {
  max-width: 400px;
}

.modal-medium {
  max-width: 600px;
}

.modal-large {
  max-width: 800px;
}

.modal-fullscreen {
  max-width: 95vw;
  max-height: 95vh;
  width: 95vw;
  height: 95vh;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.modal-title {
  flex: 1;
}

.modal-title h3 {
  margin: 0;
}

.modal-close {
  margin-left: 16px;
}

.modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.modal-footer .action-button {
  min-width: 100px;
}

/* Responsive modal */
@media (max-width: 768px) {
  .modal-content {
    margin: 20px;
    max-width: calc(100vw - 40px);
  }

  .modal-fullscreen {
    max-width: 100vw;
    max-height: 100vh;
    width: 100vw;
    height: 100vh;
    margin: 0;
    border-radius: 0;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-footer .action-button {
    width: 100%;
  }
}

/* Toast Notification System */
.toast-container {
  position: fixed;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  width: 100%;
}

.toast-container-top-center {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.toast-container-top-right {
  top: 20px;
  right: 20px;
}

.toast-container-bottom-center {
  bottom: calc(var(--bottom-nav-height) + 20px);
  left: 50%;
  transform: translateX(-50%);
}

.toast-container-bottom-right {
  bottom: calc(var(--bottom-nav-height) + 20px);
  right: 20px;
}

.toast {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  overflow: hidden;
  transition: all 0.2s ease;
  border-left: 4px solid;
}

.toast-visible {
  opacity: 1;
  transform: translateY(0);
}

.toast-hidden {
  opacity: 0;
  transform: translateY(-20px);
}

.toast-success {
  border-left-color: var(--semantic-credit);
}

.toast-error {
  border-left-color: var(--semantic-error);
}

.toast-warning {
  border-left-color: #ffc107;
}

.toast-info {
  border-left-color: var(--accent-blue);
}

.toast-content {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  gap: 12px;
}

.toast-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.toast-success .toast-icon {
  color: var(--semantic-credit);
}

.toast-error .toast-icon {
  color: var(--semantic-error);
}

.toast-warning .toast-icon {
  color: #ffc107;
}

.toast-info .toast-icon {
  color: var(--accent-blue);
}

.toast-text {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin-bottom: 4px;
}

.toast-message {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
  line-height: 1.4;
}

.toast-close {
  flex-shrink: 0;
  margin-top: -4px;
  margin-right: -4px;
}

.toast-progress {
  height: 3px;
  background-color: var(--border-color-lighter);
  overflow: hidden;
}

.toast-progress-bar {
  height: 100%;
  transition: width 0.1s linear;
}

.toast-success .toast-progress-bar {
  background-color: var(--semantic-credit);
}

.toast-error .toast-progress-bar {
  background-color: var(--semantic-error);
}

.toast-warning .toast-progress-bar {
  background-color: #ffc107;
}

.toast-info .toast-progress-bar {
  background-color: var(--accent-blue);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .toast-container {
    max-width: calc(100vw - 40px);
    margin: 0 20px;
  }

  .toast-container-top-center,
  .toast-container-bottom-center {
    left: 20px;
    right: 20px;
    transform: none;
  }
}

/* Loading Components */
.loading-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
}

.loading-spinner.medium {
  width: 24px;
  height: 24px;
}

.loading-spinner.large {
  width: 32px;
  height: 32px;
}

/* Loading Bar */
.loading-bar {
  width: 100%;
  background-color: var(--border-color-lighter);
  overflow: hidden;
  border-radius: 2px;
}

.loading-bar-fill {
  height: 100%;
  background-color: var(--primary-green);
  transition: width 0.3s ease;
}

.loading-bar.indeterminate .loading-bar-fill {
  animation: indeterminateProgress 2s infinite linear;
  width: 30% !important;
}

@keyframes indeterminateProgress {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(400%); }
}

/* Global Loading Bar */
.global-loading-bar {
  position: fixed;
  top: var(--header-height);
  left: 0;
  right: 0;
  z-index: 1050;
}

/* Skeleton Loading */
.skeleton {
  background: linear-gradient(90deg,
    var(--border-color-lighter) 25%,
    var(--border-color) 50%,
    var(--border-color-lighter) 75%
  );
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton.text {
  border-radius: 4px;
}

.skeleton.rectangular {
  border-radius: 4px;
}

.skeleton.circular {
  border-radius: 50%;
}

/* Skeleton Card */
.skeleton-card {
  padding: 16px;
}

.skeleton-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.skeleton-card-header-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-line {
  margin-bottom: 4px;
}

/* Skeleton List */
.skeleton-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
}

.skeleton-list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
}

.loading-overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
}

.loading-overlay-message {
  margin: 0;
  font-size: var(--font-size-body);
  color: var(--text-color-primary);
  text-align: center;
}

/* Page Loading */
.page-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  gap: 16px;
}

.page-loading-message {
  margin: 0;
  font-size: var(--font-size-body);
  color: var(--text-color-secondary);
  text-align: center;
}

/* Time Tabs Component */
.time-tabs {
  margin-bottom: 24px;
}

.time-tabs-container {
  display: flex;
  background-color: var(--border-color-lighter);
  border-radius: var(--border-radius-small);
  padding: 4px;
  gap: 2px;
}

.time-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 12px 16px;
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.time-tab:hover {
  background-color: var(--primary-green-light-tint);
  color: var(--primary-green);
}

.time-tab.active {
  background-color: var(--primary-green);
  color: var(--text-color-on-primary);
  box-shadow: 0 2px 4px rgba(46, 139, 87, 0.2);
}

/* Dashboard Screen */
#screen-dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px;
}

/* Welcome Card */
#welcome-card-dashboard {
  background-color: var(--primary-green-extra-light-tint);
  border-radius: var(--border-radius);
  padding: 48px 32px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 40px auto;
}

#welcome-card-dashboard h2 {
  font-size: var(--font-size-hero);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin: 0 0 16px 0;
}

#welcome-card-dashboard p {
  font-size: var(--font-size-body);
  color: var(--text-color-secondary);
  margin: 0 0 32px 0;
  line-height: 1.5;
}

.welcome-cta {
  font-size: var(--font-size-body);
  padding: 16px 32px;
}

/* Dashboard Data Content */
#dashboard-data-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Time Tabs for Dashboard */
.time-tabs {
  display: flex;
  background-color: var(--border-color-lighter);
  border-radius: var(--border-radius-small);
  padding: 4px;
  gap: 2px;
  margin-bottom: 0;
}

/* Balance Summary Card */
.balance-summary {
  padding: 24px;
}

.balance-summary h2 {
  font-size: var(--font-size-card-header);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin: 0 0 20px 0;
}

.balance-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.balance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color-lighter);
}

.balance-item:last-child {
  border-bottom: none;
}

.balance-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.balance-amount {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
}

/* Spending Categories Card */
.spending-categories-card {
  padding: 24px;
}

.spending-categories-card h2 {
  font-size: var(--font-size-card-header);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin: 0 0 20px 0;
}

.spending-categories-content {
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

.donut-chart-container {
  flex-shrink: 0;
}

.category-legend {
  flex: 1;
  min-width: 0;
}

/* Recent Transactions Card */
.recent-transactions-card {
  padding: 24px;
}

.recent-transactions-card h2 {
  font-size: var(--font-size-card-header);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin: 0 0 20px 0;
}

/* Chart Tooltip */
#chart-tooltip {
  position: absolute;
  background-color: var(--text-color-primary);
  color: var(--text-color-on-primary);
  padding: 8px 12px;
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  pointer-events: none;
  z-index: 1000;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}

/* Hover effects for chart segments */
.chart-segment:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* Legend item hover effects */
.legend-item:hover {
  background-color: var(--primary-green-light-tint);
  transform: translateX(4px);
}

.legend-item:hover .color-dot {
  transform: scale(1.2);
}

.time-tab-label {
  display: block;
}

/* Compact variant */
.time-tabs.compact .time-tabs-container {
  max-width: 300px;
}

.time-tabs.compact .time-tab {
  padding: 10px 20px;
  font-size: var(--font-size-small);
}

/* Responsive time tabs */
@media (max-width: 768px) {
  .time-tabs-container {
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px;
  }

  .time-tab {
    flex: none;
    min-width: 80px;
    padding: 8px 12px;
    font-size: var(--font-size-small);
  }

  .time-tabs.compact .time-tab {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .time-tab {
    padding: 6px 10px;
    font-size: 12px;
  }
}

/* Balance Summary Card */
.balance-summary-card {
  margin-bottom: 24px;
}

.balance-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.balance-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: var(--primary-green-extra-light-tint);
  border-radius: var(--border-radius-small);
  border-left: 4px solid var(--primary-green-light-tint);
}

.balance-item-header {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.balance-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--card-bg);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.balance-item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.balance-item-label {
  color: var(--text-color-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.balance-item-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-value {
  font-size: 11px;
  font-weight: var(--font-weight-semibold);
}

.balance-item-amount {
  text-align: right;
}

.amount-value {
  display: block;
  line-height: 1.2;
}

.balance-summary-insight {
  padding: 16px;
  background-color: var(--light-tint-grey);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color-lighter);
}

.insight-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Responsive balance summary */
@media (max-width: 768px) {
  .balance-items {
    gap: 16px;
  }

  .balance-item {
    padding: 12px;
  }

  .balance-item-icon {
    width: 36px;
    height: 36px;
  }

  .amount-value {
    font-size: var(--font-size-card-header);
  }

  /* Dashboard responsive styles */
  #screen-dashboard {
    gap: 20px;
    padding: 12px;
  }

  #welcome-card-dashboard {
    padding: 32px 24px;
    margin: 20px auto;
  }

  #welcome-card-dashboard h2 {
    font-size: var(--font-size-title);
  }

  .spending-categories-content {
    flex-direction: column;
    gap: 24px;
    align-items: center;
  }

  .donut-chart-container {
    align-self: center;
  }

  .balance-amount {
    font-size: var(--font-size-card-header);
  }
}

@media (max-width: 480px) {
  .balance-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .balance-item-amount {
    align-self: stretch;
    text-align: left;
  }

  .amount-value {
    font-size: var(--font-size-body);
    font-weight: var(--font-weight-bold);
  }

  /* Dashboard mobile styles */
  #screen-dashboard {
    gap: 16px;
    padding: 8px;
  }

  #welcome-card-dashboard {
    padding: 24px 16px;
    margin: 16px auto;
  }

  #welcome-card-dashboard h2 {
    font-size: var(--font-size-card-header);
  }

  .welcome-cta {
    padding: 12px 24px;
    font-size: var(--font-size-small);
  }

  .time-tabs {
    flex-direction: column;
    gap: 8px;
  }

  .time-tab {
    padding: 12px;
    text-align: center;
  }

  .balance-amount {
    font-size: var(--font-size-body);
  }

  .balance-label {
    font-size: var(--font-size-small);
  }
}

/* Donut Chart Component */
.donut-chart-container {
  position: relative;
  display: inline-block;
}

.donut-chart-svg {
  transform: rotate(-90deg); /* Start from top */
}

.donut-chart-segment {
  cursor: pointer;
  transition: all 0.2s ease;
}

.donut-chart-segment:hover {
  filter: brightness(1.1);
}

.donut-chart-segment.hovered {
  filter: brightness(1.2);
}

.donut-chart-background {
  opacity: 0.1;
}

.donut-chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.donut-chart-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-color);
  border-radius: 50%;
  background-color: var(--light-tint-grey);
}

.empty-chart-content {
  text-align: center;
}

/* Donut Chart Tooltip */
.donut-chart-tooltip {
  position: absolute;
  z-index: 1000;
  pointer-events: none;
  margin-bottom: 8px;
}

.tooltip-content {
  background-color: var(--text-color-primary);
  color: var(--text-color-on-primary);
  padding: 8px 12px;
  border-radius: var(--border-radius-small);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  white-space: nowrap;
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.tooltip-color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.tooltip-category {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
}

.tooltip-details {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tooltip-amount {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-semibold);
}

.tooltip-percentage {
  font-size: var(--font-size-small);
  opacity: 0.8;
}

/* Responsive donut chart */
@media (max-width: 768px) {
  .donut-chart-container {
    max-width: 180px;
    max-height: 180px;
  }
}

@media (max-width: 480px) {
  .donut-chart-container {
    max-width: 160px;
    max-height: 160px;
  }

  .tooltip-content {
    padding: 6px 10px;
  }

  .tooltip-category,
  .tooltip-amount,
  .tooltip-percentage {
    font-size: 12px;
  }
}

/* Chart Legend Component */
.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: var(--border-radius-small);
  transition: all 0.2s ease;
  position: relative;
}

.legend-item.clickable {
  cursor: pointer;
}

.legend-item.clickable:hover {
  background-color: var(--primary-green-light-tint);
  transform: translateX(4px);
}

.legend-item.selected {
  background-color: var(--primary-green-light-tint);
  border: 1px solid var(--primary-green);
}

.legend-item-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.legend-color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid var(--card-bg);
  box-shadow: 0 0 0 1px var(--border-color-lighter);
}

.legend-color-dot.others-dot {
  background: linear-gradient(45deg, #BDC3C7 25%, transparent 25%),
              linear-gradient(-45deg, #BDC3C7 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #BDC3C7 75%),
              linear-gradient(-45deg, transparent 75%, #BDC3C7 75%);
  background-size: 4px 4px;
  background-position: 0 0, 0 2px, 2px -2px, -2px 0px;
}

.legend-check-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary-green);
  background-color: var(--card-bg);
  border-radius: 50%;
  padding: 1px;
}

.legend-item-content {
  flex: 1;
  min-width: 0;
}

.legend-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 4px;
}

.legend-category {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.legend-percentage {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-secondary);
  flex-shrink: 0;
}

.legend-item-amount {
  display: flex;
  align-items: center;
}

.legend-amount-value {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
}

.legend-item.others {
  border-top: 1px solid var(--border-color-lighter);
  margin-top: 8px;
  padding-top: 16px;
}

.legend-item.others .legend-category {
  font-style: italic;
}

.legend-actions {
  padding-top: 12px;
  border-top: 1px solid var(--border-color-lighter);
  display: flex;
  justify-content: center;
}

.legend-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 24px;
  text-align: center;
}

/* Compact variant */
.chart-legend.compact .legend-items {
  gap: 8px;
}

.chart-legend.compact .legend-item {
  padding: 8px;
}

.chart-legend.compact .legend-color-dot {
  width: 10px;
  height: 10px;
}

.chart-legend.compact .legend-category,
.chart-legend.compact .legend-percentage,
.chart-legend.compact .legend-amount-value {
  font-size: 12px;
}

/* Responsive legend */
@media (max-width: 768px) {
  .legend-item {
    padding: 10px;
  }

  .legend-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .legend-percentage {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .chart-legend {
    gap: 12px;
  }

  .legend-items {
    gap: 8px;
  }

  .legend-item {
    padding: 8px;
  }

  .legend-category,
  .legend-percentage,
  .legend-amount-value {
    font-size: 12px;
  }
}

/* Recent Transactions List Component */
.recent-transactions-card {
  margin-bottom: 24px;
}

.transactions-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color-lighter);
  transition: all 0.2s ease;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-item.clickable {
  cursor: pointer;
  margin: 0 -16px;
  padding: 16px;
  border-radius: var(--border-radius-small);
}

.transaction-item.clickable:hover {
  background-color: var(--primary-green-light-tint);
  transform: translateX(4px);
}

.transaction-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--light-tint-grey);
  border-radius: 50%;
  flex-shrink: 0;
}

.transaction-icon .category-icon {
  opacity: 0.8;
}

.transaction-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transaction-main {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
}

.transaction-description {
  flex: 1;
  min-width: 0;
}

.description-text {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vendor-text {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
  display: block;
  margin-top: 2px;
}

.transaction-amount {
  flex-shrink: 0;
}

.amount-text {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-semibold);
}

.transaction-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.date-text {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
}

.category-text {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
  background-color: var(--border-color-lighter);
  padding: 2px 8px;
  border-radius: 12px;
}

.transactions-footer {
  padding-top: 16px;
  text-align: center;
  border-top: 1px solid var(--border-color-lighter);
  margin-top: 16px;
}

.transactions-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 32px 16px;
  text-align: center;
}

/* Compact variant */
.recent-transactions-card.compact .transaction-item {
  padding: 12px 0;
}

.recent-transactions-card.compact .transaction-icon {
  width: 32px;
  height: 32px;
}

.recent-transactions-card.compact .description-text {
  font-size: var(--font-size-small);
}

.recent-transactions-card.compact .amount-text {
  font-size: var(--font-size-small);
}

.recent-transactions-card.compact .vendor-text,
.recent-transactions-card.compact .date-text,
.recent-transactions-card.compact .category-text {
  font-size: 12px;
}

/* Responsive transactions list */
@media (max-width: 768px) {
  .transactions-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .transaction-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .transaction-amount {
    align-self: flex-end;
  }

  .transaction-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .transaction-item {
    padding: 12px 0;
  }

  .transaction-item.clickable {
    margin: 0 -12px;
    padding: 12px;
  }

  .transaction-icon {
    width: 36px;
    height: 36px;
  }

  .description-text {
    font-size: var(--font-size-small);
  }

  .amount-text {
    font-size: var(--font-size-small);
  }

  .vendor-text,
  .date-text,
  .category-text {
    font-size: 12px;
  }
}

/* Transactions Screen */
.transactions-screen {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Welcome Screen for Transactions */
.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  gap: 32px;
}

.welcome-header h1 {
  font-size: var(--font-size-hero);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: 8px;
}

.welcome-header p {
  font-size: var(--font-size-body);
  color: var(--text-color-secondary);
  margin: 0;
}

.welcome-empty-state {
  max-width: 500px;
  width: 100%;
}

/* Transaction Controls Section */
.transaction-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.search-bar-container {
  flex: 1;
  min-width: 250px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: var(--text-color-secondary);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--light-tint-grey);
  font-size: var(--font-size-body);
  font-family: var(--font-family);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px var(--primary-green-light-tint);
}

.clear-search-button {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.clear-search-button:hover {
  background-color: var(--border-color-lighter);
  color: var(--text-color-primary);
}

.filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-select {
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  background-color: var(--card-bg);
  font-size: var(--font-size-body);
  font-family: var(--font-family);
  color: var(--text-color-primary);
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  min-width: 140px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px var(--primary-green-light-tint);
}

.filter-select:hover {
  border-color: var(--primary-green);
}

/* Transaction List Styles */
.transaction-date-group {
  margin-bottom: 16px;
}

.transaction-date-group-header {
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  padding: 12px 0 8px 0;
  border-bottom: 1px solid var(--border-color-lighter);
  margin-bottom: 8px;
  font-size: var(--font-size-body);
}

.transaction-list-item-grouped {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color-lighter);
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.transaction-list-item-grouped:last-child {
  border-bottom: none;
}

.transaction-list-item-grouped.clickable {
  cursor: pointer;
  margin: 0 -16px;
  padding: 12px 16px;
  border-radius: var(--border-radius-small);
}

.transaction-list-item-grouped.clickable:hover {
  background-color: var(--primary-green-extra-light-tint);
  transform: translateX(4px);
}

.transaction-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--primary-green-extra-light-tint);
  border-radius: 50%;
  flex-shrink: 0;
}

.transaction-icon-container .category-icon {
  color: var(--primary-green);
}

.transaction-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transaction-description {
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  font-size: var(--font-size-body);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.transaction-date-category {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
}

.transaction-amount {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-body);
  flex-shrink: 0;
}

.empty-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  gap: 16px;
}

.empty-state-container p {
  color: var(--text-color-secondary);
  font-size: var(--font-size-body);
  margin: 0;
}

.transactions-header {
  text-align: center;
  margin-bottom: 8px;
}

.transactions-header h1 {
  margin-bottom: 8px;
}

.transactions-empty-state {
  margin-top: 40px;
}

.transactions-empty-state .empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 40px 20px;
  text-align: center;
}

/* Transactions Filters */
.transactions-filters {
  margin-bottom: 24px;
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.search-input {
  grid-column: 1;
}

.filters-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid var(--border-color-lighter);
}

/* Transactions List */
.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.transactions-day-group {
  margin-bottom: 8px;
}

.day-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.day-title {
  margin: 0;
  color: var(--text-color-primary);
}

.day-count {
  flex-shrink: 0;
}

.day-transactions {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.transaction-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color-lighter);
  transition: all 0.2s ease;
}

.transaction-row:last-child {
  border-bottom: none;
}

.transaction-row.clickable {
  cursor: pointer;
  margin: 0 -16px;
  padding: 16px;
  border-radius: var(--border-radius-small);
}

.transaction-row.clickable:hover {
  background-color: var(--primary-green-light-tint);
  transform: translateX(4px);
}

.transaction-row .transaction-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--light-tint-grey);
  border-radius: 50%;
  flex-shrink: 0;
}

.transaction-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transaction-main {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
}

.transaction-description {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.transaction-amount {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-semibold);
  flex-shrink: 0;
}

.transaction-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.transaction-time {
  color: var(--text-color-secondary);
}

.transaction-category {
  color: var(--text-color-secondary);
  background-color: var(--border-color-lighter);
  padding: 2px 8px;
  border-radius: 12px;
}

.transaction-vendor {
  color: var(--text-color-secondary);
}

/* No Results */
.no-results .no-results-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px 20px;
  text-align: center;
}

/* Responsive transactions screen */
@media (max-width: 768px) {
  .transactions-screen {
    gap: 20px;
  }

  .transaction-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-bar-container {
    min-width: auto;
  }

  .filters {
    justify-content: space-between;
  }

  .filter-select {
    flex: 1;
    min-width: auto;
  }

  .transaction-list-item-grouped {
    flex-wrap: wrap;
    gap: 8px;
  }

  .transaction-list-item-grouped.clickable {
    margin: 0 -12px;
    padding: 12px;
  }

  .transaction-content {
    flex: 1;
    min-width: 200px;
  }

  .transaction-amount {
    margin-left: auto;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .search-input {
    grid-column: 1;
  }

  .day-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .transaction-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .transaction-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .welcome-header h1 {
    font-size: var(--font-size-title);
  }

  .transaction-controls {
    gap: 8px;
  }

  .filters {
    flex-direction: column;
    gap: 8px;
  }

  .filter-select {
    width: 100%;
  }

  .transaction-icon-container {
    width: 28px;
    height: 28px;
  }

  .transaction-description {
    font-size: var(--font-size-small);
  }

  .transaction-amount {
    font-size: var(--font-size-small);
  }

  .transaction-date-category {
    font-size: 12px;
  }

  .transactions-header {
    text-align: left;
  }

  .transaction-row {
    padding: 12px 0;
  }

  .transaction-row.clickable {
    margin: 0 -12px;
    padding: 12px;
  }

  .transaction-row .transaction-icon {
    width: 36px;
    height: 36px;
  }
}

/* Insights Screen */
.insights-screen {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Insights Empty State */
.insights-empty-state-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.insights-empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 24px;
  max-width: 400px;
}

.insights-empty-state-content h3 {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin: 0;
}

.insights-empty-state-content p {
  font-size: var(--font-size-body);
  color: var(--text-color-secondary);
  margin: 0;
  line-height: 1.5;
}

.empty-state-cta {
  margin-top: 8px;
}

/* Insights List */
.insights-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 16px;
}

/* Insight Card */
.insight-card {
  display: flex;
  gap: 15px;
  align-items: flex-start;
  background-color: var(--primary-green-extra-light-tint);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.insight-card-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--card-bg);
  border-radius: 50%;
  flex-shrink: 0;
}

.insight-card-content {
  flex: 1;
  min-width: 0;
}

.insight-card-content h3 {
  font-size: var(--font-size-card-header);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
}

.insight-card-content p {
  font-size: var(--font-size-body);
  color: var(--text-color-secondary);
  margin: 0;
  line-height: 1.5;
}

/* Highlight amounts in insight descriptions */
.highlight-amount {
  font-weight: var(--font-weight-bold);
  color: var(--primary-green-dark);
}

/* Placeholder card styling */
.placeholder-card {
  background-color: var(--light-tint-grey);
}

.placeholder-card .insight-card-icon-container {
  background-color: var(--card-bg);
}

.insights-header {
  text-align: center;
  margin-bottom: 8px;
}

.insights-header h1 {
  margin-bottom: 8px;
}

.insights-empty-state {
  margin-top: 40px;
}

.insights-empty-state .empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 40px 20px;
  text-align: center;
}

/* Insights Summary */
.insights-summary {
  margin-bottom: 24px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.summary-stat {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: var(--light-tint-grey);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color-lighter);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-weight: var(--font-weight-semibold);
}

/* Insights List */
.insights-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-item {
  transition: all 0.2s ease;
}

.insight-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

/* No Insights */
.no-insights .no-insights-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px 20px;
  text-align: center;
}

/* Responsive insights screen */
@media (max-width: 768px) {
  .insights-screen {
    gap: 20px;
  }

  .insights-list {
    padding: 0 12px;
    gap: 12px;
  }

  .insight-card {
    padding: 16px;
    gap: 12px;
  }

  .insight-card-icon-container {
    width: 36px;
    height: 36px;
  }

  .insight-card-content h3 {
    font-size: var(--font-size-body);
  }

  .insight-card-content p {
    font-size: var(--font-size-small);
  }

  .insights-empty-state-content {
    gap: 20px;
    padding: 20px;
  }

  .insights-empty-state-content h3 {
    font-size: var(--font-size-card-header);
  }

  .summary-stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .summary-stat {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .insights-list {
    padding: 0 8px;
  }

  .insight-card {
    padding: 12px;
    gap: 10px;
  }

  .insight-card-icon-container {
    width: 32px;
    height: 32px;
  }

  .insight-card-content h3 {
    font-size: var(--font-size-small);
  }

  .insight-card-content p {
    font-size: 13px;
  }

  .insights-empty-state-content {
    gap: 16px;
  }

  .insights-header {
    text-align: left;
  }

  .summary-stat {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .stat-content {
    align-self: stretch;
    text-align: center;
  }
}

/* Settings Screen */
.settings-screen {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-header {
  text-align: center;
  margin-bottom: 8px;
}

.settings-header h1 {
  margin-bottom: 8px;
}

.settings-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Profile Management */
.profile-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.profile-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color-lighter);
}

.profile-item:last-child {
  border-bottom: none;
}

.profile-label {
  color: var(--text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.profile-value {
  color: var(--text-color-primary);
  text-align: right;
}

.profile-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* Data Management */
.data-actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 16px;
  background-color: var(--light-tint-grey);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color-lighter);
}

.action-info {
  flex: 1;
  min-width: 0;
}

.action-title {
  margin: 0 0 4px 0;
  color: var(--text-color-primary);
}

.action-description {
  margin: 0;
  line-height: 1.4;
}

/* Preferences */
.preferences-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preference-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color-lighter);
}

.preference-item:last-child {
  border-bottom: none;
}

.preference-info {
  flex: 1;
  min-width: 0;
}

.preference-title {
  margin: 0 0 4px 0;
  color: var(--text-color-primary);
}

.preference-description {
  margin: 0;
  line-height: 1.4;
}

.preference-value {
  flex-shrink: 0;
  padding: 4px 8px;
  background-color: var(--border-color-lighter);
  border-radius: var(--border-radius-small);
  font-weight: var(--font-weight-medium);
}

/* App Information */
.app-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color-lighter);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: var(--text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  color: var(--text-color-primary);
  text-align: right;
}

/* Password Form */
.password-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Responsive settings screen */
@media (max-width: 768px) {
  .settings-screen {
    gap: 20px;
  }

  .profile-actions {
    flex-direction: column;
  }

  .action-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .preference-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .preference-value {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .settings-header {
    text-align: left;
  }

  .profile-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .profile-value {
    text-align: left;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .info-value {
    text-align: left;
  }
}

/* Enhanced Hover Effects */
.card:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.action-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.nav-item:hover {
  transform: translateY(-1px);
}

.upload-zone:hover {
  transform: scale(1.01);
}

.donut-chart-segment:hover {
  filter: brightness(1.15) saturate(1.1);
}

.legend-item.clickable:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Focus States */
.action-button:focus,
.nav-item:focus,
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: 2px solid var(--primary-green);
  outline-offset: 2px;
}

/* Active States */
.action-button:active {
  transform: translateY(0);
}

.nav-item:active {
  transform: scale(0.98);
}

/* Smooth Transitions */
* {
  transition: transform 0.2s ease, box-shadow 0.2s ease, filter 0.2s ease;
}

/* Custom Tooltip Component */
.tooltip-trigger {
  display: inline-block;
}

.tooltip {
  position: fixed;
  z-index: 1200;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.tooltip-content {
  background-color: var(--text-color-primary);
  color: var(--text-color-on-primary);
  padding: 8px 12px;
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-small);
  line-height: 1.4;
  max-width: 250px;
  word-wrap: break-word;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.tooltip-arrow-top {
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px 5px 0 5px;
  border-color: var(--text-color-primary) transparent transparent transparent;
}

.tooltip-arrow-bottom {
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent var(--text-color-primary) transparent;
}

.tooltip-arrow-left {
  right: -5px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 5px 0 5px 5px;
  border-color: transparent transparent transparent var(--text-color-primary);
}

.tooltip-arrow-right {
  left: -5px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 5px 5px 5px 0;
  border-color: transparent var(--text-color-primary) transparent transparent;
}

/* Tooltip variants */
.tooltip.success .tooltip-content {
  background-color: var(--semantic-credit);
}

.tooltip.success .tooltip-arrow-top {
  border-color: var(--semantic-credit) transparent transparent transparent;
}

.tooltip.success .tooltip-arrow-bottom {
  border-color: transparent transparent var(--semantic-credit) transparent;
}

.tooltip.success .tooltip-arrow-left {
  border-color: transparent transparent transparent var(--semantic-credit);
}

.tooltip.success .tooltip-arrow-right {
  border-color: transparent var(--semantic-credit) transparent transparent;
}

.tooltip.error .tooltip-content {
  background-color: var(--semantic-error);
}

.tooltip.error .tooltip-arrow-top {
  border-color: var(--semantic-error) transparent transparent transparent;
}

.tooltip.error .tooltip-arrow-bottom {
  border-color: transparent transparent var(--semantic-error) transparent;
}

.tooltip.error .tooltip-arrow-left {
  border-color: transparent transparent transparent var(--semantic-error);
}

.tooltip.error .tooltip-arrow-right {
  border-color: transparent var(--semantic-error) transparent transparent;
}

/* Enhanced Loading Animations */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Loading states for specific components */
.card.loading {
  position: relative;
  overflow: hidden;
}

.card.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Button loading states */
.action-button.loading {
  position: relative;
  color: transparent;
}

.action-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Micro-animations */

/* Card entrance animations */
.card {
  animation: cardEnter 0.3s ease-out;
}

@keyframes cardEnter {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered card animations */
.card:nth-child(1) { animation-delay: 0ms; }
.card:nth-child(2) { animation-delay: 50ms; }
.card:nth-child(3) { animation-delay: 100ms; }
.card:nth-child(4) { animation-delay: 150ms; }
.card:nth-child(5) { animation-delay: 200ms; }

/* Button click animations */
.action-button {
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.action-button:active::before {
  width: 300px;
  height: 300px;
}

/* Screen transition animations */
.screen-enter {
  animation: screenEnter 0.4s ease-out;
}

.screen-exit {
  animation: screenExit 0.3s ease-in;
}

@keyframes screenEnter {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes screenExit {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

/* List item animations */
.transaction-item,
.legend-item,
.insight-item {
  animation: listItemEnter 0.3s ease-out;
}

@keyframes listItemEnter {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Staggered list animations */
.transaction-item:nth-child(1) { animation-delay: 0ms; }
.transaction-item:nth-child(2) { animation-delay: 30ms; }
.transaction-item:nth-child(3) { animation-delay: 60ms; }
.transaction-item:nth-child(4) { animation-delay: 90ms; }
.transaction-item:nth-child(5) { animation-delay: 120ms; }

/* Tab switching animations */
.tab-content {
  animation: tabContentEnter 0.3s ease-out;
}

@keyframes tabContentEnter {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modal animations */
.modal-overlay {
  animation: modalOverlayEnter 0.2s ease-out;
}

.modal-content {
  animation: modalContentEnter 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalOverlayEnter {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalContentEnter {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Toast animations */
.toast {
  animation: toastEnter 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes toastEnter {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Chart animations */
.donut-chart-segment {
  animation: chartSegmentEnter 0.6s ease-out;
  animation-fill-mode: both;
}

@keyframes chartSegmentEnter {
  from {
    stroke-dasharray: 0 1000;
  }
  to {
    stroke-dasharray: var(--segment-length, 0) 1000;
  }
}

/* Hover micro-animations */
.card-hover:hover {
  animation: cardHover 0.2s ease-out;
}

@keyframes cardHover {
  0% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
  100% { transform: translateY(-2px); }
}

/* Focus animations */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  animation: inputFocus 0.2s ease-out;
}

@keyframes inputFocus {
  from {
    box-shadow: 0 0 0 0 var(--primary-green-light-tint);
  }
  to {
    box-shadow: 0 0 0 3px var(--primary-green-light-tint);
  }
}

/* Number counter animation */
.hero-amount,
.stat-value {
  animation: numberCountUp 0.8s ease-out;
}

@keyframes numberCountUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Progress bar animation */
.loading-bar-fill {
  animation: progressFill 0.3s ease-out;
}

@keyframes progressFill {
  from {
    width: 0;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Enhanced Responsive Design */

/* Breakpoint variables */
:root {
  --breakpoint-xs: 320px;
  --breakpoint-sm: 480px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1200px;
}

/* Base responsive utilities */
.container {
  width: 100%;
  max-width: var(--breakpoint-xl);
  margin: 0 auto;
  padding: 0 16px;
}

/* Responsive grid system */
.grid {
  display: grid;
  gap: 16px;
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Mobile-first responsive breakpoints */

/* Extra small devices (phones, 320px and up) */
@media (min-width: 320px) {
  .container {
    padding: 0 12px;
  }

  .app-main {
    padding: 16px;
  }

  .card {
    padding: 16px;
  }

  .hero-amount {
    font-size: 24px;
  }
}

/* Small devices (phones, 480px and up) */
@media (min-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .app-main {
    padding: 20px;
  }

  .card {
    padding: 20px;
  }

  .grid-sm-cols-2 { grid-template-columns: repeat(2, 1fr); }

  .hero-amount {
    font-size: var(--font-size-hero);
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .container {
    padding: 0 24px;
  }

  .app-main {
    padding: 24px;
  }

  .card {
    padding: 24px;
  }

  .grid-md-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-md-cols-3 { grid-template-columns: repeat(3, 1fr); }

  /* Enhanced tablet layouts */
  .spending-categories-content {
    grid-template-columns: auto 1fr;
    gap: 32px;
  }

  .filters-grid {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }

  .summary-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Large devices (desktops, 1024px and up) */
@media (min-width: 1024px) {
  .container {
    padding: 0 32px;
  }

  .app-main {
    padding: 32px;
  }

  .grid-lg-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-lg-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-lg-cols-4 { grid-template-columns: repeat(4, 1fr); }

  /* Desktop enhancements */
  #screen-dashboard {
    gap: 32px;
    padding: 24px;
  }

  #welcome-card-dashboard {
    padding: 64px 48px;
  }

  .spending-categories-content {
    gap: 48px;
  }

  .balance-items {
    gap: 20px;
  }

  .balance-item {
    padding: 20px 0;
  }

  .dashboard-screen {
    gap: 32px;
  }

  .upload-screen {
    gap: 32px;
  }

  .transactions-screen {
    gap: 32px;
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .app-main {
    padding: 40px;
  }

  .grid-xl-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-xl-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-xl-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-xl-cols-5 { grid-template-columns: repeat(5, 1fr); }
}

/* Touch-friendly improvements for mobile */
@media (max-width: 767px) {
  /* Larger touch targets */
  .action-button {
    min-height: 44px;
    padding: 12px 20px;
  }

  .nav-item {
    min-height: 44px;
    padding: 8px 12px;
  }

  .form-input,
  .form-select,
  .form-textarea {
    min-height: 44px;
    padding: 12px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Mobile-optimized spacing */
  .card {
    margin-bottom: 16px;
  }

  .card-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .card-body {
    margin-bottom: 12px;
  }

  /* Mobile navigation improvements */
  #bottom-nav-bar {
    padding: 8px 0;
    height: 56px;
  }

  .nav-item {
    padding: 6px 8px;
    gap: 2px;
    min-width: 50px;
  }

  .nav-label {
    font-size: 11px;
  }

  .app-main {
    padding-bottom: 72px;
  }

  /* Mobile modal improvements */
  .modal-content {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }

  /* Mobile form improvements */
  .filters-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .analysis-buttons {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .profile-actions {
    flex-direction: column;
    gap: 8px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .icon svg {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape orientation optimizations */
@media (orientation: landscape) and (max-height: 500px) {
  .app-header {
    height: 50px;
  }

  .content-wrapper {
    padding-top: 50px;
  }

  #bottom-nav-bar {
    height: 50px;
  }

  .app-main {
    padding-bottom: 66px;
    padding: 16px 16px 66px 16px;
  }

  .modal-content {
    max-height: 80vh;
  }
}

/* Print styles */
@media print {
  .app-header,
  #bottom-nav-bar,
  .action-button,
  .nav-item {
    display: none !important;
  }

  .app-main {
    margin: 0;
    padding: 0;
  }

  .card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .text-credit {
    color: #000 !important;
  }

  .text-debit {
    color: #000 !important;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    --card-bg: #1a1a1a;
    --light-tint-grey: #2a2a2a;
    --text-color-primary: #ffffff;
    --text-color-secondary: #cccccc;
    --border-color: #404040;
    --border-color-lighter: #333333;
  }
}

/* Accessibility Improvements */

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Enhanced focus indicators */
*:focus {
  outline: 2px solid var(--primary-green);
  outline-offset: 2px;
}

.action-button:focus,
.nav-item:focus,
.icon-button:focus {
  outline: 3px solid var(--primary-green);
  outline-offset: 2px;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: 2px solid var(--primary-green);
  outline-offset: 1px;
  border-color: var(--primary-green);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-green: #000000;
    --text-color-primary: #000000;
    --text-color-secondary: #000000;
    --border-color: #000000;
    --card-bg: #ffffff;
    --light-tint-grey: #ffffff;
  }

  .action-button {
    border: 2px solid #000000;
  }

  .card {
    border: 2px solid #000000;
  }
}

/* Keyboard navigation improvements */
.keyboard-navigation .action-button:focus,
.keyboard-navigation .nav-item:focus,
.keyboard-navigation .icon-button:focus {
  background-color: var(--primary-green-light-tint);
  transform: scale(1.05);
}

.keyboard-navigation .card:focus {
  outline: 3px solid var(--primary-green);
  outline-offset: 2px;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-green);
  color: var(--text-color-on-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--border-radius-small);
  z-index: 1000;
  font-weight: var(--font-weight-semibold);
}

.skip-link:focus {
  top: 6px;
}

/* ARIA live regions */
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Error states for accessibility */
.form-input[aria-invalid="true"],
.form-select[aria-invalid="true"],
.form-textarea[aria-invalid="true"] {
  border-color: var(--semantic-error);
  background-color: var(--semantic-error-bg);
}

.form-input[aria-invalid="true"]:focus,
.form-select[aria-invalid="true"]:focus,
.form-textarea[aria-invalid="true"]:focus {
  outline-color: var(--semantic-error);
  box-shadow: 0 0 0 3px var(--semantic-error-bg);
}

/* Loading states for screen readers */
.loading[aria-busy="true"] {
  position: relative;
}

.loading[aria-busy="true"]::after {
  content: "Loading...";
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Button states for screen readers */
.action-button[aria-pressed="true"] {
  background-color: var(--primary-green-dark);
}

.action-button[aria-expanded="true"] {
  background-color: var(--primary-green-light-tint);
}

/* Table accessibility */
.table {
  border-collapse: collapse;
  width: 100%;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background-color: var(--light-tint-grey);
  font-weight: var(--font-weight-semibold);
}

.table caption {
  caption-side: top;
  padding: 12px;
  font-weight: var(--font-weight-semibold);
  text-align: left;
}

/* Form accessibility */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-weight: var(--font-weight-medium);
}

.form-label.required::after {
  content: " *";
  color: var(--semantic-error);
}

.form-help {
  margin-top: 4px;
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
}

.form-error {
  margin-top: 4px;
  font-size: var(--font-size-small);
  color: var(--semantic-error);
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Modal accessibility */
.modal-overlay[aria-hidden="true"] {
  display: none;
}

.modal-content[role="dialog"] {
  position: relative;
}

.modal-content[role="dialog"]:focus {
  outline: none;
}

/* Toast accessibility */
.toast[role="alert"] {
  position: relative;
}

.toast[role="status"] {
  position: relative;
}

/* Navigation accessibility */
.nav-item[aria-current="page"] {
  background-color: var(--primary-green-light-tint);
  color: var(--primary-green);
}

/* Chart accessibility */
.donut-chart-svg[role="img"] {
  position: relative;
}

.chart-data-table {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.chart-data-table:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  padding: 16px;
  margin-top: 16px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .card:hover {
    transform: none;
  }

  .action-button:hover {
    transform: none;
  }

  .nav-item:hover {
    transform: none;
  }
}

/* Performance Optimizations */

/* GPU acceleration for smooth animations */
.card,
.action-button,
.nav-item,
.modal-content,
.toast,
.donut-chart-segment {
  will-change: transform;
  transform: translateZ(0);
}

/* Optimize repaints and reflows */
.card:hover,
.action-button:hover,
.nav-item:hover {
  will-change: transform, box-shadow;
}

/* Contain layout and style changes */
.card {
  contain: layout style;
}

.transaction-item,
.legend-item {
  contain: layout;
}

/* Optimize font rendering */
body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Optimize image rendering */
img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Optimize scrolling performance */
.app-main,
.modal-body,
.transactions-list {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Reduce paint complexity */
.donut-chart-svg {
  shape-rendering: geometricPrecision;
}

/* Optimize transitions for 60fps */
* {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Critical CSS - inline these styles */
.critical {
  /* App shell styles that should be inlined */
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Lazy loading placeholder */
.lazy-placeholder {
  background: linear-gradient(90deg,
    var(--border-color-lighter) 25%,
    var(--border-color) 50%,
    var(--border-color-lighter) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--border-radius);
}

/* Intersection observer optimization */
.intersection-item {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.intersection-item.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Virtualization support */
.virtual-list {
  height: 400px;
  overflow-y: auto;
  contain: strict;
}

.virtual-item {
  contain: layout style paint;
}

/* Memory optimization */
.offscreen {
  content-visibility: hidden;
}

/* Reduce layout thrashing */
.layout-stable {
  contain: layout;
  height: min-content;
}

/* Optimize for mobile performance */
@media (max-width: 767px) {
  /* Reduce animation complexity on mobile */
  .card:hover {
    transform: none;
    transition: none;
  }

  /* Optimize touch scrolling */
  .app-main {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Reduce repaints on mobile */
  .action-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* Preload critical resources */
.preload-fonts {
  font-family: var(--font-family);
  font-weight: var(--font-weight-normal);
  font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-semibold);
  font-weight: var(--font-weight-bold);
  visibility: hidden;
  position: absolute;
}

/* Optimize for high refresh rate displays */
@media (min-resolution: 120dpi) {
  * {
    transition-duration: 0.1s;
  }

  .card:hover {
    transition-duration: 0.15s;
  }
}

/* Battery optimization */
@media (prefers-reduced-motion: reduce) {
  .lazy-placeholder {
    animation: none;
    background: var(--border-color-lighter);
  }

  .intersection-item {
    opacity: 1;
    transform: none;
    transition: none;
  }
}

/* Network-aware optimizations */
@media (prefers-reduced-data: reduce) {
  .donut-chart-svg {
    shape-rendering: optimizeSpeed;
  }

  .card {
    box-shadow: none;
    border: 1px solid var(--border-color);
  }
}

/* Legacy loading class */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: var(--font-size-body);
  color: var(--text-color-secondary);
}

/* Application Header */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70px;
  background-color: var(--primary-green);
  color: var(--text-color-on-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
}

/* Content wrapper to account for fixed header */
.content-wrapper {
  padding-top: 70px;
}

/* Header title container */
.header-title-container {
  flex-grow: 1;
  text-align: left;
  margin-right: 16px; /* Reduced margin for better spacing */
}

#header-title {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-on-primary);
  margin: 0 0 4px 0;
}

#header-subtitle {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-regular);
  color: var(--text-color-on-primary);
  opacity: 0.9;
  margin: 0;
}

/* Profile dropdown container */
.profile-dropdown-container {
  position: relative;
  flex-shrink: 0; /* Prevent shrinking */
  z-index: 1001; /* Ensure dropdown appears above other content */
}

/* Profile avatar */
.profile-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid var(--text-color-on-primary);
  background-color: var(--primary-green-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-on-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.profile-avatar:hover {
  background-color: var(--primary-green-light-tint);
  color: var(--primary-green);
  transform: scale(1.05);
}

.profile-avatar:focus {
  outline: 2px solid var(--text-color-on-primary);
  outline-offset: 2px;
}

.profile-avatar.hidden {
  display: none;
}

/* Profile dropdown menu */
.profile-dropdown-menu {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  padding: 8px 0;
  margin: 0;
  list-style: none;
  z-index: 1001;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.2s ease;
}

.profile-dropdown-menu.hidden {
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
}

.profile-dropdown-menu li {
  margin: 0;
  padding: 0;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-regular);
  color: var(--text-color-primary);
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border-color-lighter);
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: var(--primary-green-extra-light-tint);
}

.dropdown-item:focus {
  outline: 2px solid var(--primary-green);
  outline-offset: -2px;
  background-color: var(--primary-green-extra-light-tint);
}

/* Header responsive styles */
@media (max-width: 768px) {
  .app-header {
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-title-container {
    margin-right: 12px;
    flex-grow: 1;
  }

  #header-title {
    font-size: var(--font-size-card-header);
  }

  #header-subtitle {
    font-size: 12px;
  }

  .profile-avatar {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-small);
  }

  .profile-dropdown-menu {
    min-width: 140px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 6px 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-title-container {
    margin-right: 8px;
    flex-grow: 1;
    min-width: 0; /* Allow text to shrink */
  }

  #header-title {
    font-size: var(--font-size-body);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  #header-subtitle {
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .profile-dropdown-container {
    flex-shrink: 0;
    position: relative;
  }

  .profile-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .dropdown-item {
    padding: 10px 12px;
    font-size: var(--font-size-small);
  }
}

/* Bottom Navigation Bar */
#bottom-nav-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: var(--card-bg);
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10px 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

#bottom-nav-bar.hidden {
  opacity: 0;
  transform: translateY(100%);
  pointer-events: none;
}

/* Main content padding to account for bottom nav */
.app-main {
  padding-bottom: 80px;
}

/* Navigation Items */
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--border-radius-small);
  transition: all 0.2s ease;
  color: var(--text-color-secondary);
  min-width: 60px;
  gap: 4px;
}

.nav-item:hover {
  background-color: var(--primary-green-extra-light-tint);
  transform: translateY(-1px);
}

.nav-item:focus {
  outline: 2px solid var(--primary-green);
  outline-offset: 2px;
}

.nav-item:active {
  transform: scale(0.98);
}

/* Active state */
.nav-item.active {
  color: var(--primary-green);
}

.nav-item.active .nav-icon {
  color: var(--primary-green);
  stroke: var(--primary-green);
}

/* Navigation icon */
.nav-icon {
  color: var(--text-color-secondary);
  stroke: var(--text-color-secondary);
  transition: all 0.2s ease;
}

/* Navigation label */
.nav-label {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  margin: 0;
  text-align: center;
  line-height: 1.2;
}