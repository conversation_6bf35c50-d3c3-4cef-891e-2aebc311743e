import React from 'react';
import { Icon } from './Icon';

export type ScreenType = 'dashboard' | 'upload' | 'transactions' | 'insights' | 'settings';

interface NavigationItem {
  id: ScreenType;
  label: string;
  icon: string;
  dataScreen: string;
}

interface BottomNavigationProps {
  currentScreen: ScreenType;
  onScreenChange: (screen: ScreenType) => void;
  isVisible?: boolean;
  className?: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'home',
    dataScreen: 'dashboard'
  },
  {
    id: 'upload',
    label: 'Upload',
    icon: 'upload',
    dataScreen: 'upload'
  },
  {
    id: 'transactions',
    label: 'Transactions',
    icon: 'list',
    dataScreen: 'transactions'
  },
  {
    id: 'insights',
    label: 'Insights',
    icon: 'bar-chart-2',
    dataScreen: 'insights'
  }
];

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  currentScreen,
  onScreenChange,
  isVisible = true,
  className = ''
}) => {
  const handleNavClick = (screenId: ScreenType) => {
    if (screenId !== currentScreen) {
      onScreenChange(screenId);
    }
  };

  return (
    <nav
      id="bottom-nav-bar"
      className={`bottom-nav ${!isVisible ? 'hidden' : ''} ${className}`}
    >
      {navigationItems.map((item) => {
        const isActive = currentScreen === item.id;

        return (
          <button
            key={item.id}
            className={`nav-item ${isActive ? 'active' : ''}`}
            data-screen={item.dataScreen}
            onClick={() => handleNavClick(item.id)}
            aria-label={`Navigate to ${item.label}`}
            aria-current={isActive ? 'page' : undefined}
          >
            <Icon
              name={item.icon}
              size={24}
              strokeWidth={isActive ? 2.5 : 2}
              className={`nav-icon ${isActive ? 'active' : ''}`}
            />
            <span className="nav-label">{item.label}</span>
          </button>
        );
      })}
    </nav>
  );
};

// Hook for managing navigation state
export const useNavigation = (initialScreen: ScreenType = 'dashboard') => {
  const [currentScreen, setCurrentScreen] = React.useState<ScreenType>(initialScreen);

  const navigateTo = (screen: ScreenType) => {
    setCurrentScreen(screen);
  };

  const goBack = () => {
    // Simple back navigation - could be enhanced with history stack
    setCurrentScreen('dashboard');
  };

  return {
    currentScreen,
    navigateTo,
    goBack
  };
};
