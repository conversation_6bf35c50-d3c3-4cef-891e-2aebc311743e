import React from 'react';
import * as feather from 'feather-icons';

interface IconProps {
  name: string;
  size?: number;
  strokeWidth?: number;
  className?: string;
  color?: string;
}

export const Icon: React.FC<IconProps> = ({ 
  name, 
  size = 24, 
  strokeWidth = 2, 
  className = '',
  color 
}) => {
  const iconSvg = feather.icons[name as keyof typeof feather.icons];
  
  if (!iconSvg) {
    console.warn(`Icon "${name}" not found in feather-icons`);
    return null;
  }

  const svgString = iconSvg.toSvg({
    width: size,
    height: size,
    'stroke-width': strokeWidth,
    ...(color && { stroke: color })
  });

  return (
    <span 
      className={`icon ${className}`}
      dangerouslySetInnerHTML={{ __html: svgString }}
    />
  );
};

// Common icon configurations
export const NavigationIcon: React.FC<Omit<IconProps, 'size' | 'strokeWidth'>> = (props) => (
  <Icon {...props} size={24} strokeWidth={2} />
);

export const ActiveNavigationIcon: React.FC<Omit<IconProps, 'size' | 'strokeWidth'>> = (props) => (
  <Icon {...props} size={24} strokeWidth={2.5} />
);

export const CardIcon: React.FC<Omit<IconProps, 'size' | 'strokeWidth'>> = (props) => (
  <Icon {...props} size={20} strokeWidth={2} />
);

export const SmallIcon: React.FC<Omit<IconProps, 'size' | 'strokeWidth'>> = (props) => (
  <Icon {...props} size={18} strokeWidth={2} />
);

// Category icon mapping for transactions
export const getCategoryIcon = (category: string): string => {
  const categoryIconMap: Record<string, string> = {
    'Add Money (Wallet Top-Up)': 'plus-circle',
    'Bank Deposit': 'credit-card',
    'Cash Deposit': 'dollar-sign',
    'Interest Earned': 'trending-up',
    'Investment Payback': 'bar-chart-2',
    'Refund': 'rotate-ccw',
    'Loan Disbursement': 'briefcase',
    'Commission': 'award',
    'Utilities': 'zap',
    'Airtime': 'phone',
    'Data Bundle': 'wifi',
    'TV & Subscriptions': 'tv',
    'Online Shopping': 'shopping-cart',
    'Card Payment': 'credit-card',
    'POS Payment': 'terminal',
    'QR Code Payment': 'smartphone',
    'Bank Transfer': 'send',
    'Transportation': 'truck',
    'Food & Dining': 'coffee',
    'Entertainment': 'music',
    'Healthcare': 'heart',
    'Education': 'book',
    'Groceries': 'shopping-bag',
    'Fuel': 'zap',
    'ATM Withdrawal': 'credit-card',
    'Cash Withdrawal': 'dollar-sign',
    'Investment': 'trending-up',
    'Savings': 'piggy-bank',
    'Insurance': 'shield',
    'Loan Payment': 'credit-card',
    'Tax Payment': 'file-text',
    'Government Fee': 'file-text',
    'Bank Charges': 'minus-circle',
    'Transaction Fee': 'minus-circle',
    'Maintenance Fee': 'tool',
    'Penalty': 'alert-triangle',
    'Other Income': 'plus',
    'Other Expense': 'minus',
    'Uncategorized': 'help-circle'
  };

  return categoryIconMap[category] || 'help-circle';
};
