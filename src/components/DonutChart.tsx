import React, { useState, useRef } from 'react';

export interface ChartDataItem {
  category: string;
  amount: number;
  color: string;
  percentage: number;
}

interface DonutChartProps {
  data: ChartDataItem[];
  size?: number;
  strokeWidth?: number;
  className?: string;
  onSegmentHover?: (item: ChartDataItem | null) => void;
  onSegmentClick?: (item: ChartDataItem) => void;
  centerContent?: React.ReactNode;
  showTooltip?: boolean;
}

interface TooltipProps {
  item: ChartDataItem;
  position: { x: number; y: number };
  visible: boolean;
}

const Tooltip: React.FC<TooltipProps> = ({ item, position, visible }) => {
  if (!visible) return null;

  return (
    <div 
      className="donut-chart-tooltip"
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -100%)'
      }}
    >
      <div className="tooltip-content">
        <div className="tooltip-header">
          <div 
            className="tooltip-color-dot" 
            style={{ backgroundColor: item.color }}
          />
          <span className="tooltip-category">{item.category}</span>
        </div>
        <div className="tooltip-details">
          <span className="tooltip-amount">₦{item.amount.toLocaleString()}</span>
          <span className="tooltip-percentage">({item.percentage.toFixed(1)}%)</span>
        </div>
      </div>
    </div>
  );
};

export const DonutChart: React.FC<DonutChartProps> = ({
  data,
  size = 200,
  strokeWidth = 30,
  className = '',
  onSegmentHover,
  onSegmentClick,
  centerContent,
  showTooltip = true
}) => {
  const [hoveredItem, setHoveredItem] = useState<ChartDataItem | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const svgRef = useRef<SVGSVGElement>(null);

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const center = size / 2;

  // Calculate cumulative percentages for positioning
  let cumulativePercentage = 0;
  const segments = data.map(item => {
    const startPercentage = cumulativePercentage;
    cumulativePercentage += item.percentage;
    
    const startAngle = (startPercentage / 100) * 360 - 90; // Start from top
    const endAngle = (cumulativePercentage / 100) * 360 - 90;
    const strokeDasharray = `${(item.percentage / 100) * circumference} ${circumference}`;
    const strokeDashoffset = -((startPercentage / 100) * circumference);

    return {
      ...item,
      startAngle,
      endAngle,
      strokeDasharray,
      strokeDashoffset
    };
  });

  const handleMouseEnter = (item: ChartDataItem, event: React.MouseEvent) => {
    setHoveredItem(item);
    if (onSegmentHover) {
      onSegmentHover(item);
    }

    if (showTooltip && svgRef.current) {
      const rect = svgRef.current.getBoundingClientRect();
      setTooltipPosition({
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      });
    }
  };

  const handleMouseLeave = () => {
    setHoveredItem(null);
    if (onSegmentHover) {
      onSegmentHover(null);
    }
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (hoveredItem && showTooltip && svgRef.current) {
      const rect = svgRef.current.getBoundingClientRect();
      setTooltipPosition({
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      });
    }
  };

  const handleClick = (item: ChartDataItem) => {
    if (onSegmentClick) {
      onSegmentClick(item);
    }
  };

  if (data.length === 0) {
    return (
      <div className={`donut-chart-empty ${className}`} style={{ width: size, height: size }}>
        <div className="empty-chart-content">
          <span className="small-text text-secondary">No data available</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`donut-chart-container ${className}`} style={{ width: size, height: size }}>
      <svg
        ref={svgRef}
        width={size}
        height={size}
        className="donut-chart-svg"
        onMouseMove={handleMouseMove}
      >
        {/* Background circle */}
        <circle
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke="var(--border-color-lighter)"
          strokeWidth={strokeWidth}
          className="donut-chart-background"
        />

        {/* Data segments */}
        {segments.map((segment, index) => (
          <circle
            key={`${segment.category}-${index}`}
            cx={center}
            cy={center}
            r={radius}
            fill="none"
            stroke={segment.color}
            strokeWidth={strokeWidth}
            strokeDasharray={segment.strokeDasharray}
            strokeDashoffset={segment.strokeDashoffset}
            className={`donut-chart-segment ${hoveredItem?.category === segment.category ? 'hovered' : ''}`}
            style={{
              transform: hoveredItem?.category === segment.category ? 'scale(1.05)' : 'scale(1)',
              transformOrigin: `${center}px ${center}px`,
              transition: 'transform 0.2s ease'
            }}
            onMouseEnter={(e) => handleMouseEnter(segment, e)}
            onMouseLeave={handleMouseLeave}
            onClick={() => handleClick(segment)}
          />
        ))}
      </svg>

      {/* Center content */}
      {centerContent && (
        <div className="donut-chart-center">
          {centerContent}
        </div>
      )}

      {/* Tooltip */}
      {showTooltip && hoveredItem && (
        <Tooltip
          item={hoveredItem}
          position={tooltipPosition}
          visible={!!hoveredItem}
        />
      )}
    </div>
  );
};

// Utility function to generate chart data from transactions
export const generateChartData = (categoryBreakdown: Record<string, number>): ChartDataItem[] => {
  const total = Object.values(categoryBreakdown).reduce((sum, amount) => sum + amount, 0);
  
  if (total === 0) return [];

  // Predefined colors for categories
  const categoryColors: Record<string, string> = {
    'Transportation': '#FF6B6B',
    'Food & Dining': '#4ECDC4',
    'Online Shopping': '#45B7D1',
    'Utilities': '#96CEB4',
    'Entertainment': '#FFEAA7',
    'Healthcare': '#DDA0DD',
    'Education': '#98D8C8',
    'Groceries': '#F7DC6F',
    'Airtime': '#BB8FCE',
    'Data Bundle': '#85C1E9',
    'Other': '#BDC3C7'
  };

  const defaultColors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];

  return Object.entries(categoryBreakdown)
    .filter(([_, amount]) => amount > 0)
    .sort(([_, a], [__, b]) => b - a) // Sort by amount descending
    .map(([category, amount], index) => ({
      category,
      amount,
      percentage: (amount / total) * 100,
      color: categoryColors[category] || defaultColors[index % defaultColors.length] || '#BDC3C7'
    }));
};
