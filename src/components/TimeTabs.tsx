import React from 'react';

export type TimeRange = 'this-month' | 'last-month' | 'last-3-months' | 'last-6-months' | 'this-year';

interface TimeTab {
  id: TimeRange;
  label: string;
  shortLabel?: string;
}

interface TimeTabsProps {
  activeTab: TimeRange;
  onTabChange: (tab: TimeRange) => void;
  className?: string;
  variant?: 'default' | 'compact';
}

const timeTabs: TimeTab[] = [
  {
    id: 'this-month',
    label: 'This Month',
    shortLabel: 'This Month'
  },
  {
    id: 'last-month',
    label: 'Last Month',
    shortLabel: 'Last Month'
  },
  {
    id: 'last-3-months',
    label: 'Last 3 Months',
    shortLabel: '3 Months'
  },
  {
    id: 'last-6-months',
    label: 'Last 6 Months',
    shortLabel: '6 Months'
  },
  {
    id: 'this-year',
    label: 'This Year',
    shortLabel: 'This Year'
  }
];

export const TimeTabs: React.FC<TimeTabsProps> = ({
  activeTab,
  onTabChange,
  className = '',
  variant = 'default'
}) => {
  const tabsToShow = variant === 'compact' 
    ? timeTabs.slice(0, 2) // Only show This Month and Last Month for compact
    : timeTabs;

  return (
    <div className={`time-tabs ${variant} ${className}`}>
      <div className="time-tabs-container">
        {tabsToShow.map((tab) => (
          <button
            key={tab.id}
            className={`time-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => onTabChange(tab.id)}
            aria-pressed={activeTab === tab.id}
          >
            <span className="time-tab-label">
              {variant === 'compact' && tab.shortLabel ? tab.shortLabel : tab.label}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

// Utility functions for date filtering
export const getDateRangeForTab = (tab: TimeRange): { start: Date; end: Date } => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth();

  switch (tab) {
    case 'this-month': {
      const start = new Date(currentYear, currentMonth, 1);
      const end = new Date(currentYear, currentMonth + 1, 0, 23, 59, 59, 999);
      return { start, end };
    }
    
    case 'last-month': {
      const start = new Date(currentYear, currentMonth - 1, 1);
      const end = new Date(currentYear, currentMonth, 0, 23, 59, 59, 999);
      return { start, end };
    }
    
    case 'last-3-months': {
      const start = new Date(currentYear, currentMonth - 3, 1);
      const end = new Date(currentYear, currentMonth + 1, 0, 23, 59, 59, 999);
      return { start, end };
    }
    
    case 'last-6-months': {
      const start = new Date(currentYear, currentMonth - 6, 1);
      const end = new Date(currentYear, currentMonth + 1, 0, 23, 59, 59, 999);
      return { start, end };
    }
    
    case 'this-year': {
      const start = new Date(currentYear, 0, 1);
      const end = new Date(currentYear, 11, 31, 23, 59, 59, 999);
      return { start, end };
    }
    
    default:
      return getDateRangeForTab('this-month');
  }
};

export const formatDateRangeLabel = (tab: TimeRange): string => {
  const { start, end } = getDateRangeForTab(tab);
  
  const formatOptions: Intl.DateTimeFormatOptions = {
    month: 'short',
    year: 'numeric'
  };

  switch (tab) {
    case 'this-month':
      return `${start.toLocaleDateString('en-US', formatOptions)}`;
    
    case 'last-month':
      return `${start.toLocaleDateString('en-US', formatOptions)}`;
    
    case 'last-3-months':
      return `${start.toLocaleDateString('en-US', formatOptions)} - ${end.toLocaleDateString('en-US', formatOptions)}`;
    
    case 'last-6-months':
      return `${start.toLocaleDateString('en-US', formatOptions)} - ${end.toLocaleDateString('en-US', formatOptions)}`;
    
    case 'this-year':
      return `${start.getFullYear()}`;
    
    default:
      return '';
  }
};

// Hook for managing time tab state and filtering
export const useTimeTabs = (initialTab: TimeRange = 'this-month') => {
  const [activeTab, setActiveTab] = React.useState<TimeRange>(initialTab);

  const dateRange = React.useMemo(() => getDateRangeForTab(activeTab), [activeTab]);
  const dateRangeLabel = React.useMemo(() => formatDateRangeLabel(activeTab), [activeTab]);

  const isDateInRange = React.useCallback((date: string | Date) => {
    const targetDate = typeof date === 'string' ? new Date(date) : date;
    return targetDate >= dateRange.start && targetDate <= dateRange.end;
  }, [dateRange]);

  const filterTransactionsByDate = React.useCallback(<T extends { date: string | Date }>(transactions: T[]): T[] => {
    return transactions.filter(transaction => isDateInRange(transaction.date));
  }, [isDateInRange]);

  return {
    activeTab,
    setActiveTab,
    dateRange,
    dateRangeLabel,
    isDateInRange,
    filterTransactionsByDate
  };
};
