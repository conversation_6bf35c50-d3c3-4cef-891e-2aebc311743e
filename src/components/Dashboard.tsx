import React, { useMemo, useState } from 'react';
import { Transaction } from '../types';
import { useTimeTabs } from './TimeTabs';
import { DonutChart, generateChartData } from './DonutChart';
import { ChartLegend, useLegendSelection } from './ChartLegend';
import { RecentTransactionsList } from './RecentTransactionsList';
import { ProcessingLogs } from './ProcessingLogs';
import { ErrorLogs } from './ErrorLogs';
import { Button } from './Button';
import { Icon } from './Icon';
import { useToastContext } from './Toast';

interface DashboardProps {
  transactions: Transaction[];
  onNavigateToTransactions?: (categoryFilter?: string) => void;
  onNavigateToUpload?: () => void;
  userId?: string;
  showAdvancedFeatures?: boolean;
}

export const Dashboard: React.FC<DashboardProps> = ({
  transactions,
  onNavigateToTransactions,
  onNavigateToUpload,
  userId,
  showAdvancedFeatures = false
}) => {
  const toast = useToastContext();
  const [activeSection, setActiveSection] = useState<'overview' | 'logs' | 'errors'>('overview');
  const {
    activeTab,
    setActiveTab,
    filterTransactionsByDate
  } = useTimeTabs('this-month');

  // Filter transactions by selected time range
  const filteredTransactions = useMemo(() => {
    return filterTransactionsByDate(transactions);
  }, [transactions, filterTransactionsByDate]);

  // Calculate stats for filtered transactions
  const filteredStats = useMemo(() => {
    const income = filteredTransactions
      .filter(t => t.type === 'credit')
      .reduce((sum, t) => sum + t.amount, 0);

    const expenses = filteredTransactions
      .filter(t => t.type === 'debit')
      .reduce((sum, t) => sum + t.amount, 0);

    const categoryBreakdown: Record<string, number> = {};
    filteredTransactions
      .filter(t => t.type === 'debit')
      .forEach(t => {
        const category = t.category || 'Uncategorized';
        categoryBreakdown[category] = (categoryBreakdown[category] || 0) + t.amount;
      });

    return {
      totalIncome: income,
      totalExpenses: expenses,
      netCashFlow: income - expenses,
      transactionCount: filteredTransactions.length,
      categoryBreakdown
    };
  }, [filteredTransactions]);

  // Generate chart data
  const chartData = useMemo(() => {
    return generateChartData(filteredStats.categoryBreakdown);
  }, [filteredStats.categoryBreakdown]);

  // Legend selection state
  const { selectedItems, handleItemClick } = useLegendSelection();

  // Handle category filter from legend
  const handleCategoryFilter = (category: string) => {
    if (onNavigateToTransactions) {
      onNavigateToTransactions(category);
      toast.info('Filtered by Category', `Showing transactions for ${category}`);
    }
  };

  // Handle view all transactions
  const handleViewAllTransactions = () => {
    if (onNavigateToTransactions) {
      onNavigateToTransactions();
    }
  };

  // Handle upload navigation
  const handleUploadClick = () => {
    if (onNavigateToUpload) {
      onNavigateToUpload();
    }
  };

  // Empty state - Welcome Card
  if (transactions.length === 0) {
    return (
      <section id="screen-dashboard" className="screen">
        <div id="welcome-card-dashboard" className="welcome-card">
          <h2>Welcome to FinScope!</h2>
          <p>Ready to get a clear view of your finances? Let's analyze your first statement.</p>
          <Button
            variant="primary"
            size="large"
            icon="file-plus"
            onClick={handleUploadClick}
            className="welcome-cta"
          >
            Analyze Your First Statement
          </Button>
        </div>
      </section>
    );
  }

  return (
    <section id="screen-dashboard" className="screen">
      <div id="dashboard-data-content">
        {/* Time Filter Tabs */}
        <div className="time-tabs">
          <button
            className={`time-tab ${activeTab === 'this-month' ? 'active' : ''}`}
            onClick={() => setActiveTab('this-month')}
          >
            This Month
          </button>
          <button
            className={`time-tab ${activeTab === 'last-month' ? 'active' : ''}`}
            onClick={() => setActiveTab('last-month')}
          >
            Last Month
          </button>
        </div>

        {/* Balance Summary Card */}
        <div className="balance-summary card">
          <h2>Balance Summary</h2>
          <div className="balance-items">
            <div className="balance-item">
              <div className="balance-label">
                <Icon name="arrow-up-circle" size={20} className="text-credit" />
                <span>Money In:</span>
              </div>
              <div id="total-credit" className="balance-amount text-credit">
                ₦{filteredStats.totalIncome.toLocaleString()}
              </div>
            </div>
            <div className="balance-item">
              <div className="balance-label">
                <Icon name="arrow-down-circle" size={20} className="text-debit" />
                <span>Money Out:</span>
              </div>
              <div id="total-debit" className="balance-amount text-debit">
                ₦{filteredStats.totalExpenses.toLocaleString()}
              </div>
            </div>
            <div className="balance-item">
              <div className="balance-label">
                <Icon name="dollar-sign" size={20} className="text-primary" />
                <span>Net Cash Flow:</span>
              </div>
              <div
                id="net-cash-flow"
                className={`balance-amount ${filteredStats.netCashFlow >= 0 ? 'text-credit' : 'text-debit'}`}
              >
                ₦{filteredStats.netCashFlow.toLocaleString()}
              </div>
            </div>
          </div>
        </div>

        {/* Spending Categories Card */}
        {chartData.length > 0 && (
          <div className="spending-categories-card card">
            <h2>Spending Categories</h2>
            <div className="spending-categories-content">
              <div className="donut-chart-container">
                <DonutChart
                  data={chartData}
                  size={200}
                  onSegmentClick={(item) => handleCategoryFilter(item.category)}
                  centerContent={
                    <div className="chart-center-content">
                      <div className="center-amount hero-amount text-debit">
                        ₦{filteredStats.totalExpenses.toLocaleString()}
                      </div>
                      <div className="center-label small-text text-secondary">
                        Total Spent
                      </div>
                    </div>
                  }
                />
              </div>
              <div className="category-legend">
                <ChartLegend
                  data={chartData}
                  selectedItems={selectedItems}
                  onItemClick={(item) => {
                    handleItemClick(item);
                    handleCategoryFilter(item.category);
                  }}
                  maxItems={6}
                />
              </div>
            </div>
          </div>
        )}

        {/* Recent Transactions Card */}
        <div className="recent-transactions-card card">
          <h2>Recent Transactions</h2>
          <RecentTransactionsList
            transactions={filteredTransactions}
            maxItems={5}
            onViewAll={handleViewAllTransactions}
            onTransactionClick={(transaction) => {
              toast.info('Transaction Details', `${transaction.description} - ₦${transaction.amount.toLocaleString()}`);
            }}
          />
        </div>

        {/* Advanced Features Section */}
        {showAdvancedFeatures && userId && (
          <div className="advanced-features-section">
            {/* Section Navigation */}
            <div className="section-nav card">
              <div className="flex space-x-2">
                <Button
                  variant={activeSection === 'overview' ? 'primary' : 'outline'}
                  size="small"
                  onClick={() => setActiveSection('overview')}
                  icon="bar-chart-2"
                >
                  Overview
                </Button>
                <Button
                  variant={activeSection === 'logs' ? 'primary' : 'outline'}
                  size="small"
                  onClick={() => setActiveSection('logs')}
                  icon="file-text"
                >
                  Processing Logs
                </Button>
                <Button
                  variant={activeSection === 'errors' ? 'primary' : 'outline'}
                  size="small"
                  onClick={() => setActiveSection('errors')}
                  icon="alert-circle"
                >
                  Error Logs
                </Button>
              </div>
            </div>

            {/* Section Content */}
            {activeSection === 'logs' && (
              <div className="processing-logs-section card">
                <ProcessingLogs userId={userId} limit={10} />
              </div>
            )}

            {activeSection === 'errors' && (
              <div className="error-logs-section card">
                <ErrorLogs userId={userId} limit={10} />
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
};