import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardBody } from './Card';
import { Button } from './Button';
import { Input } from './Input';
import { Icon } from './Icon';
import { <PERSON><PERSON>, <PERSON>dalHeader, <PERSON>dal<PERSON><PERSON>, ModalFooter, ConfirmModal } from './Modal';
import { useToastContext } from './Toast';
import { authService } from '../services/auth';
import { apiService } from '../services/api';

interface SettingsScreenProps {
  user: any; // User type from your auth system
  onSignOut: () => void;
}

export const SettingsScreen: React.FC<SettingsScreenProps> = ({ user, onSignOut }) => {
  const toast = useToastContext();
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const handlePasswordChange = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('Password Mismatch', 'New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast.error('Password Too Short', 'Password must be at least 6 characters long');
      return;
    }

    setIsLoading(true);
    try {
      // Implement password change logic here
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Password Updated', 'Your password has been changed successfully');
      setShowPasswordModal(false);
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
    } catch (error) {
      toast.error('Password Change Failed', 'Unable to update password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAllData = async () => {
    setIsLoading(true);
    try {
      await apiService.deleteAllData();
      toast.success('Data Deleted', 'All your financial data has been permanently deleted');
      setShowDeleteModal(false);
    } catch (error) {
      toast.error('Delete Failed', 'Unable to delete data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportData = async () => {
    setIsLoading(true);
    try {
      const data = await apiService.exportData();
      
      // Create and download file
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `finscope-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast.success('Data Exported', 'Your data has been downloaded successfully');
      setShowExportModal(false);
    } catch (error) {
      toast.error('Export Failed', 'Unable to export data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await authService.signOut();
      toast.success('Signed Out', 'You have been signed out successfully');
      onSignOut();
    } catch (error) {
      toast.error('Sign Out Failed', 'Unable to sign out. Please try again.');
    }
  };

  return (
    <div className="settings-screen">
      <div className="settings-header">
        <h1 className="screen-title">Settings</h1>
        <p className="body-text text-secondary">
          Manage your account and application preferences
        </p>
      </div>

      {/* Profile Management */}
      <Card className="settings-section">
        <CardHeader>
          <div className="section-header">
            <Icon name="user" size={20} className="text-primary" />
            <h3 className="card-header">Profile Management</h3>
          </div>
        </CardHeader>
        <CardBody>
          <div className="profile-info">
            <div className="profile-item">
              <span className="profile-label small-text-medium">Email</span>
              <span className="profile-value body-text">{user?.email || 'Not available'}</span>
            </div>
            <div className="profile-item">
              <span className="profile-label small-text-medium">Display Name</span>
              <span className="profile-value body-text">{user?.displayName || 'Not set'}</span>
            </div>
            <div className="profile-item">
              <span className="profile-label small-text-medium">Account Created</span>
              <span className="profile-value body-text">
                {user?.metadata?.creationTime 
                  ? new Date(user.metadata.creationTime).toLocaleDateString()
                  : 'Not available'
                }
              </span>
            </div>
          </div>
          
          <div className="profile-actions">
            <Button
              variant="outline"
              icon="lock"
              onClick={() => setShowPasswordModal(true)}
            >
              Change Password
            </Button>
            <Button
              variant="danger"
              icon="log-out"
              onClick={handleSignOut}
            >
              Sign Out
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Data Management */}
      <Card className="settings-section">
        <CardHeader>
          <div className="section-header">
            <Icon name="database" size={20} className="text-primary" />
            <h3 className="card-header">Data Management</h3>
          </div>
        </CardHeader>
        <CardBody>
          <div className="data-actions">
            <div className="action-item">
              <div className="action-info">
                <h4 className="action-title body-text-medium">Export Data</h4>
                <p className="action-description small-text text-secondary">
                  Download all your financial data in JSON format
                </p>
              </div>
              <Button
                variant="outline"
                icon="download"
                onClick={() => setShowExportModal(true)}
              >
                Export
              </Button>
            </div>
            
            <div className="action-item">
              <div className="action-info">
                <h4 className="action-title body-text-medium">Delete All Data</h4>
                <p className="action-description small-text text-secondary">
                  Permanently remove all transactions and analysis data
                </p>
              </div>
              <Button
                variant="danger"
                icon="trash-2"
                onClick={() => setShowDeleteModal(true)}
              >
                Delete All
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Preferences */}
      <Card className="settings-section">
        <CardHeader>
          <div className="section-header">
            <Icon name="settings" size={20} className="text-primary" />
            <h3 className="card-header">Preferences</h3>
          </div>
        </CardHeader>
        <CardBody>
          <div className="preferences-list">
            <div className="preference-item">
              <div className="preference-info">
                <h4 className="preference-title body-text-medium">Currency</h4>
                <p className="preference-description small-text text-secondary">
                  Nigerian Naira (₦)
                </p>
              </div>
              <span className="preference-value small-text text-secondary">NGN</span>
            </div>
            
            <div className="preference-item">
              <div className="preference-info">
                <h4 className="preference-title body-text-medium">Date Format</h4>
                <p className="preference-description small-text text-secondary">
                  How dates are displayed throughout the app
                </p>
              </div>
              <span className="preference-value small-text text-secondary">DD/MM/YYYY</span>
            </div>
            
            <div className="preference-item">
              <div className="preference-info">
                <h4 className="preference-title body-text-medium">Default Analysis Mode</h4>
                <p className="preference-description small-text text-secondary">
                  Preferred analysis mode for new uploads
                </p>
              </div>
              <span className="preference-value small-text text-secondary">Advanced</span>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* App Information */}
      <Card className="settings-section">
        <CardHeader>
          <div className="section-header">
            <Icon name="info" size={20} className="text-primary" />
            <h3 className="card-header">About FinScope</h3>
          </div>
        </CardHeader>
        <CardBody>
          <div className="app-info">
            <div className="info-item">
              <span className="info-label small-text-medium">Version</span>
              <span className="info-value body-text">1.0.0</span>
            </div>
            <div className="info-item">
              <span className="info-label small-text-medium">Last Updated</span>
              <span className="info-value body-text">December 2024</span>
            </div>
            <div className="info-item">
              <span className="info-label small-text-medium">Support</span>
              <span className="info-value body-text"><EMAIL></span>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Password Change Modal */}
      <Modal isOpen={showPasswordModal} onClose={() => setShowPasswordModal(false)}>
        <ModalHeader onClose={() => setShowPasswordModal(false)}>
          <h3 className="card-header">Change Password</h3>
        </ModalHeader>
        <ModalBody>
          <div className="password-form">
            <Input
              type="password"
              label="Current Password"
              value={passwordData.currentPassword}
              onChange={(value) => setPasswordData(prev => ({ ...prev, currentPassword: value }))}
              required
            />
            <Input
              type="password"
              label="New Password"
              value={passwordData.newPassword}
              onChange={(value) => setPasswordData(prev => ({ ...prev, newPassword: value }))}
              required
            />
            <Input
              type="password"
              label="Confirm New Password"
              value={passwordData.confirmPassword}
              onChange={(value) => setPasswordData(prev => ({ ...prev, confirmPassword: value }))}
              required
            />
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setShowPasswordModal(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handlePasswordChange}
            loading={isLoading}
          >
            Update Password
          </Button>
        </ModalFooter>
      </Modal>

      {/* Export Confirmation Modal */}
      <ConfirmModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        onConfirm={handleExportData}
        title="Export Data"
        message="This will download all your financial data in JSON format. The file will contain all your transactions and analysis results."
        confirmText="Export Data"
        variant="info"
        loading={isLoading}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteAllData}
        title="Delete All Data"
        message="This action cannot be undone. All your transactions, analysis results, and financial data will be permanently deleted."
        confirmText="Delete All Data"
        variant="danger"
        loading={isLoading}
      />
    </div>
  );
};
