import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface TooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  disabled?: boolean;
  className?: string;
}

interface TooltipPosition {
  x: number;
  y: number;
  position: 'top' | 'bottom' | 'left' | 'right';
}

export const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'top',
  delay = 500,
  disabled = false,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState<TooltipPosition>({
    x: 0,
    y: 0,
    position: 'top'
  });
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const calculatePosition = (triggerRect: DOMRect, preferredPosition: string): TooltipPosition => {
    const tooltipElement = tooltipRef.current;
    if (!tooltipElement) {
      return { x: 0, y: 0, position: 'top' };
    }

    const tooltipRect = tooltipElement.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const margin = 8;

    let finalPosition = preferredPosition as 'top' | 'bottom' | 'left' | 'right';
    let x = 0;
    let y = 0;

    // Calculate position based on preferred position
    switch (preferredPosition) {
      case 'top':
        x = triggerRect.left + triggerRect.width / 2;
        y = triggerRect.top - margin;
        
        // Check if tooltip fits above
        if (y - tooltipRect.height < 0) {
          finalPosition = 'bottom';
          y = triggerRect.bottom + margin;
        }
        break;

      case 'bottom':
        x = triggerRect.left + triggerRect.width / 2;
        y = triggerRect.bottom + margin;
        
        // Check if tooltip fits below
        if (y + tooltipRect.height > viewportHeight) {
          finalPosition = 'top';
          y = triggerRect.top - margin;
        }
        break;

      case 'left':
        x = triggerRect.left - margin;
        y = triggerRect.top + triggerRect.height / 2;
        
        // Check if tooltip fits to the left
        if (x - tooltipRect.width < 0) {
          finalPosition = 'right';
          x = triggerRect.right + margin;
        }
        break;

      case 'right':
        x = triggerRect.right + margin;
        y = triggerRect.top + triggerRect.height / 2;
        
        // Check if tooltip fits to the right
        if (x + tooltipRect.width > viewportWidth) {
          finalPosition = 'left';
          x = triggerRect.left - margin;
        }
        break;
    }

    // Ensure tooltip doesn't go off screen horizontally
    if (finalPosition === 'top' || finalPosition === 'bottom') {
      const halfTooltipWidth = tooltipRect.width / 2;
      if (x - halfTooltipWidth < margin) {
        x = halfTooltipWidth + margin;
      } else if (x + halfTooltipWidth > viewportWidth - margin) {
        x = viewportWidth - halfTooltipWidth - margin;
      }
    }

    // Ensure tooltip doesn't go off screen vertically
    if (finalPosition === 'left' || finalPosition === 'right') {
      const halfTooltipHeight = tooltipRect.height / 2;
      if (y - halfTooltipHeight < margin) {
        y = halfTooltipHeight + margin;
      } else if (y + halfTooltipHeight > viewportHeight - margin) {
        y = viewportHeight - halfTooltipHeight - margin;
      }
    }

    return { x, y, position: finalPosition };
  };

  const showTooltip = () => {
    if (disabled || !content) return;

    timeoutRef.current = setTimeout(() => {
      if (triggerRef.current) {
        const triggerRect = triggerRef.current.getBoundingClientRect();
        const newPosition = calculatePosition(triggerRect, position);
        setTooltipPosition(newPosition);
        setIsVisible(true);
      }
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const getTooltipStyle = (): React.CSSProperties => {
    const { x, y, position: finalPosition } = tooltipPosition;

    let transform = '';
    switch (finalPosition) {
      case 'top':
        transform = 'translate(-50%, -100%)';
        break;
      case 'bottom':
        transform = 'translate(-50%, 0%)';
        break;
      case 'left':
        transform = 'translate(-100%, -50%)';
        break;
      case 'right':
        transform = 'translate(0%, -50%)';
        break;
    }

    return {
      left: x,
      top: y,
      transform,
      opacity: isVisible ? 1 : 0,
      pointerEvents: isVisible ? 'auto' : 'none'
    };
  };

  const tooltipClasses = [
    'tooltip',
    `tooltip-${tooltipPosition.position}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onFocus={showTooltip}
        onBlur={hideTooltip}
        className="tooltip-trigger"
      >
        {children}
      </div>

      {content && createPortal(
        <div
          ref={tooltipRef}
          className={tooltipClasses}
          style={getTooltipStyle()}
          role="tooltip"
          aria-hidden={!isVisible}
        >
          <div className="tooltip-content">
            {content}
          </div>
          <div className={`tooltip-arrow tooltip-arrow-${tooltipPosition.position}`} />
        </div>,
        document.body
      )}
    </>
  );
};

// Hook for managing tooltip state
export const useTooltip = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [content, setContent] = useState<React.ReactNode>(null);

  const show = (newContent: React.ReactNode) => {
    setContent(newContent);
    setIsVisible(true);
  };

  const hide = () => {
    setIsVisible(false);
  };

  const toggle = (newContent?: React.ReactNode) => {
    if (isVisible) {
      hide();
    } else {
      show(newContent || content);
    }
  };

  return {
    isVisible,
    content,
    show,
    hide,
    toggle
  };
};
