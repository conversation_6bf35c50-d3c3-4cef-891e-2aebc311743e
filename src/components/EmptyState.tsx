import React from 'react';
import { Card, CardBody } from './Card';
import { Icon } from './Icon';
import { Button } from './Button';

interface EmptyStateProps {
  icon?: string;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    icon?: string;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  className?: string;
  size?: 'small' | 'medium' | 'large';
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'inbox',
  title,
  description,
  action,
  className = '',
  size = 'medium'
}) => {
  const iconSizes = {
    small: 32,
    medium: 48,
    large: 64
  };

  const emptyStateClasses = [
    'empty-state',
    `empty-state-${size}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <Card className={emptyStateClasses}>
      <CardBody>
        <div className="empty-state-content">
          <div className="empty-state-icon">
            <Icon name={icon} size={iconSizes[size]} className="text-secondary" />
          </div>
          
          <div className="empty-state-text">
            <h3 className="empty-state-title">{title}</h3>
            <p className="empty-state-description">{description}</p>
          </div>
          
          {action && (
            <div className="empty-state-action">
              <Button
                variant={action.variant || 'primary'}
                icon={action.icon}
                onClick={action.onClick}
                size={size === 'small' ? 'small' : 'medium'}
              >
                {action.label}
              </Button>
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

// Specialized empty state variants
interface NoDataEmptyStateProps {
  title?: string;
  description?: string;
  onUpload?: () => void;
  className?: string;
}

export const NoDataEmptyState: React.FC<NoDataEmptyStateProps> = ({
  title = 'No Data Available',
  description = 'Upload your bank statements to get started with financial analysis.',
  onUpload,
  className = ''
}) => {
  return (
    <EmptyState
      icon="upload-cloud"
      title={title}
      description={description}
      action={onUpload ? {
        label: 'Analyze Your First Statement',
        onClick: onUpload,
        icon: 'upload',
        variant: 'primary'
      } : undefined}
      className={className}
      size="large"
    />
  );
};

interface NoResultsEmptyStateProps {
  title?: string;
  description?: string;
  onClearFilters?: () => void;
  className?: string;
}

export const NoResultsEmptyState: React.FC<NoResultsEmptyStateProps> = ({
  title = 'No Results Found',
  description = 'Try adjusting your search criteria or filters to find what you\'re looking for.',
  onClearFilters,
  className = ''
}) => {
  return (
    <EmptyState
      icon="search"
      title={title}
      description={description}
      action={onClearFilters ? {
        label: 'Clear Filters',
        onClick: onClearFilters,
        icon: 'x',
        variant: 'outline'
      } : undefined}
      className={className}
      size="medium"
    />
  );
};

interface ErrorEmptyStateProps {
  title?: string;
  description?: string;
  onRetry?: () => void;
  className?: string;
}

export const ErrorEmptyState: React.FC<ErrorEmptyStateProps> = ({
  title = 'Something Went Wrong',
  description = 'We encountered an error while loading your data. Please try again.',
  onRetry,
  className = ''
}) => {
  return (
    <EmptyState
      icon="alert-circle"
      title={title}
      description={description}
      action={onRetry ? {
        label: 'Try Again',
        onClick: onRetry,
        icon: 'refresh-cw',
        variant: 'primary'
      } : undefined}
      className={className}
      size="medium"
    />
  );
};

interface ComingSoonEmptyStateProps {
  title?: string;
  description?: string;
  className?: string;
}

export const ComingSoonEmptyState: React.FC<ComingSoonEmptyStateProps> = ({
  title = 'Coming Soon',
  description = 'This feature is currently under development and will be available in a future update.',
  className = ''
}) => {
  return (
    <EmptyState
      icon="clock"
      title={title}
      description={description}
      className={className}
      size="medium"
    />
  );
};

// Hook for managing empty state logic
export const useEmptyState = (
  data: any[],
  isLoading: boolean = false,
  error: Error | null = null
) => {
  const isEmpty = !isLoading && !error && data.length === 0;
  const hasError = !isLoading && !!error;
  const hasData = !isLoading && !error && data.length > 0;

  return {
    isEmpty,
    hasError,
    hasData,
    isLoading,
    error
  };
};
