import React, { useState } from 'react';
import { authService } from '../services/auth';
import { Card, CardHeader, CardBody } from './Card';
import { Button } from './Button';
import { Input } from './Input';
import { useToastContext } from './Toast';
import { SignupScreen } from './SignupScreen';

interface AuthScreenProps {
  onSignIn: () => void;
}

export const AuthScreen: React.FC<AuthScreenProps> = ({ onSignIn }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showSignup, setShowSignup] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
  const toast = useToastContext();

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      await authService.signInWithGoogle();
      toast.success('Welcome to FinScope!', 'Successfully signed in');
      onSignIn();
    } catch (error) {
      console.error('Sign-in failed:', error);
      toast.error(
        'Sign-in Failed',
        'Unable to sign in with Google. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const validate = () => {
    const errs: typeof errors = {};
    if (!email) {
      errs.email = 'Email is required';
    } else if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      errs.email = 'Invalid email address';
    }
    if (!password) {
      errs.password = 'Password is required';
    }
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) return;
    setIsLoading(true);
    try {
      await authService.signInWithEmail(email, password);
      toast.success('Welcome to FinScope!', 'Successfully signed in');
      setEmail('');
      setPassword('');
      onSignIn();
    } catch (error: any) {
      let message = 'Sign in failed. Please try again.';
      if (error.code === 'auth/user-not-found') {
        message = 'No account found with this email.';
      } else if (error.code === 'auth/wrong-password') {
        message = 'Incorrect password.';
      } else if (error.code === 'auth/invalid-email') {
        message = 'Invalid email address.';
      }
      toast.error('Sign In Failed', message);
    } finally {
      setIsLoading(false);
    }
  };

  if (showSignup) {
    return <SignupScreen onSignUpSuccess={() => setShowSignup(false)} onNavigateToSignIn={() => setShowSignup(false)} />;
  }

  return (
    <div className="auth-screen">
      <div className="auth-container">
        <div className="auth-branding">
          <h1 className="screen-title">FinScope</h1>
          <p className="body-text text-secondary">
            Your smart financial assistant for tracking, analyzing, and summarizing transactions
          </p>
        </div>

        <Card className="auth-card">
          <CardHeader>
            <h2 className="card-header">Welcome Back</h2>
            <p className="small-text text-secondary">
              Sign in to access your financial dashboard
            </p>
          </CardHeader>

          <CardBody>
            <form onSubmit={handleEmailSignIn} className="space-y-4 mb-4">
              <Input
                label="Email"
                type="email"
                value={email}
                onChange={setEmail}
                error={errors.email}
                autoComplete="email"
                required
              />
              <Input
                label="Password"
                type="password"
                value={password}
                onChange={setPassword}
                error={errors.password}
                autoComplete="current-password"
                required
              />
              <Button
                type="submit"
                variant="primary"
                loading={isLoading}
                disabled={isLoading}
                className="w-full"
              >
                Sign in
              </Button>
            </form>

            <div className="flex items-center my-4">
              <div className="flex-grow border-t border-gray-200" />
              <span className="mx-2 text-xs text-gray-400">or</span>
              <div className="flex-grow border-t border-gray-200" />
            </div>

            <Button
              onClick={handleGoogleSignIn}
              disabled={isLoading}
              loading={isLoading}
              icon="user"
              variant="outline"
              size="large"
              fullWidth
              className="google-signin-button"
            >
              {isLoading ? 'Signing in...' : 'Sign in with Google'}
            </Button>

            <div className="auth-features">
              <div className="feature-item">
                <div className="feature-icon">🔒</div>
                <span className="small-text">Secure & Private</span>
              </div>
              <div className="feature-item">
                <div className="feature-icon">📊</div>
                <span className="small-text">Smart Analytics</span>
              </div>
              <div className="feature-item">
                <div className="feature-icon">⚡</div>
                <span className="small-text">AI-Powered</span>
              </div>
            </div>

            {/* Divider and Sign Up Button */}
            <div className="mt-8 flex flex-col items-center">
              <div className="w-full flex items-center mb-2">
                <div className="flex-grow border-t border-gray-200" />
                <span className="mx-2 text-xs text-gray-400">New here?</span>
                <div className="flex-grow border-t border-gray-200" />
              </div>
              <Button
                type="button"
                variant="outline"
                icon="user-plus"
                className="w-full mt-2"
                onClick={() => setShowSignup(true)}
                disabled={isLoading}
              >
                Create an Account
              </Button>
            </div>
          </CardBody>
        </Card>

        <div className="auth-footer">
          <p className="small-text text-secondary">
            By signing in, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </div>
    </div>
  );
};