import React, { useState, useMemo, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Transaction } from '../types';
import { Card, CardHeader, CardBody } from './Card';
import { Input, Select } from './Input';
import { Icon, getCategoryIcon } from './Icon';
import { Button } from './Button';
import { useToastContext } from './Toast';
import { NoDataEmptyState } from './EmptyState';
import { JobStatus } from './JobStatus';
import { JobHistory } from './JobHistory';
import { Modal } from './Modal';
import { apiService } from '../services/api';

interface TransactionsScreenProps {
  transactions: Transaction[];
  onTransactionClick?: (transaction: Transaction) => void;
  onNavigateToUpload?: () => void;
  initialCategoryFilter?: string;
  onClearCategoryFilter?: () => void;
}

interface FilterState {
  search: string;
  month: string;
  category: string;
}

interface GroupedTransactions {
  [date: string]: Transaction[];
}

export const TransactionsScreen: React.FC<TransactionsScreenProps> = ({
  transactions,
  onNavigateToUpload,
  initialCategoryFilter,
  onClearCategoryFilter
}) => {
  const toast = useToastContext();
  const [searchParams] = useSearchParams();
  const [showJobStatus, setShowJobStatus] = useState(false);
  const [showJobHistory, setShowJobHistory] = useState(false);
  const [_showOverflowMenu, _setShowOverflowMenu] = useState(false);
  const [showAutoCategorizeModal, setShowAutoCategorizeModal] = useState(false);
  const [autoCategorizeSelection, setAutoCategorizeSelection] = useState<string[]>([]);
  const [autoCategorizeDateRange, setAutoCategorizeDateRange] = useState<{start: string, end: string}>({start: '', end: ''});
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<{[id: string]: Partial<Transaction>}>({});
  const [autoCategorizeLoading, setAutoCategorizeLoading] = useState(false);
  const [editLoading, setEditLoading] = useState<string | null>(null);
  
  // Check for job ID in URL params
  const jobId = searchParams.get('jobId');
  const lastJobId = localStorage.getItem('lastJobId');
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    month: '',
    category: initialCategoryFilter || ''
  });

  // Load persisted filters from localStorage on component mount
  useEffect(() => {
    const savedFilters = localStorage.getItem('finscope-transaction-filters');
    if (savedFilters && !initialCategoryFilter) {
      try {
        const parsedFilters = JSON.parse(savedFilters);
        setFilters(prev => ({ ...prev, ...parsedFilters }));
      } catch (error) {
        console.warn('Failed to parse saved filters:', error);
      }
    }
  }, [initialCategoryFilter]);

  // Handle initial category filter from dashboard
  useEffect(() => {
    if (initialCategoryFilter) {
      setFilters(prev => ({ ...prev, category: initialCategoryFilter }));
      // Clear the category filter in the parent component after setting it
      if (onClearCategoryFilter) {
        onClearCategoryFilter();
      }
    }
  }, [initialCategoryFilter, onClearCategoryFilter]);

  // Save filters to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('finscope-transaction-filters', JSON.stringify(filters));
  }, [filters]);

  // Get unique months and categories for filter options
  const { monthOptions, categoryOptions } = useMemo(() => {
    const months = new Set<string>();
    const categories = new Set<string>();

    transactions.forEach(transaction => {
      const date = new Date(transaction.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const monthLabel = date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
      months.add(`${monthKey}|${monthLabel}`);
      
      if (transaction.category) {
        categories.add(transaction.category);
      }
    });

    const monthOptions = Array.from(months)
      .sort((a, b) => b.split('|')[0].localeCompare(a.split('|')[0]))
      .map(month => {
        const [value, label] = month.split('|');
        return { value, label };
      });

    const categoryOptions = Array.from(categories)
      .sort()
      .map(category => ({ value: category, label: category }));

    return { monthOptions, categoryOptions };
  }, [transactions]);

  // Helper: Get uncategorized transactions (excluding those with user-set category)
  const uncategorizedTransactions = useMemo(() =>
    transactions.filter(t => !t.category || t.category === 'Uncategorized'),
    [transactions]
  );

  // Helper: Filter uncategorized by date range
  const filteredUncategorized = useMemo(() => {
    if (!autoCategorizeDateRange.start || !autoCategorizeDateRange.end) return uncategorizedTransactions;
    const start = new Date(autoCategorizeDateRange.start);
    const end = new Date(autoCategorizeDateRange.end);
    return uncategorizedTransactions.filter(t => {
      const d = new Date(t.date);
      return d >= start && d <= end;
    });
  }, [uncategorizedTransactions, autoCategorizeDateRange]);

  // Filter transactions based on current filters
  const filteredTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const matchesSearch =
          transaction.description.toLowerCase().includes(searchTerm) ||
          (transaction.vendor && transaction.vendor.toLowerCase().includes(searchTerm)) ||
          transaction.amount.toString().includes(searchTerm);

        if (!matchesSearch) return false;
      }

      // Month filter
      if (filters.month) {
        const transactionDate = new Date(transaction.date);
        const transactionMonth = `${transactionDate.getFullYear()}-${String(transactionDate.getMonth() + 1).padStart(2, '0')}`;
        if (transactionMonth !== filters.month) return false;
      }

      // Category filter
      if (filters.category && transaction.category !== filters.category) {
        return false;
      }

      return true;
    });
  }, [transactions, filters]);

  // Group transactions by date
  const groupedTransactions = useMemo(() => {
    const grouped: GroupedTransactions = {};
    
    filteredTransactions
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .forEach(transaction => {
        const date = new Date(transaction.date).toDateString();
        if (!grouped[date]) {
          grouped[date] = [];
        }
        grouped[date].push(transaction);
      });

    return grouped;
  }, [filteredTransactions]);

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearSearch = () => {
    setFilters(prev => ({ ...prev, search: '' }));
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  };

  const formatAmount = (amount: number, type: 'credit' | 'debit') => {
    const sign = type === 'credit' ? '+' : '-';
    return `${sign}₦${amount.toLocaleString()}`;
  };

  const getAmountColorClass = (type: 'credit' | 'debit') => {
    return type === 'credit' ? 'text-credit' : 'text-debit';
  };

  // Show job status if job ID is present
  useEffect(() => {
    if (jobId || lastJobId) {
      setShowJobStatus(true);
    }
  }, [jobId, lastJobId]);

  // Show job status or history when no transactions but job is active
  if (transactions.length === 0 && (showJobStatus || showJobHistory)) {
    return (
      <section id="screen-transactions" className="screen">
        <div className="welcome-container">
          <div className="welcome-header">
            <h1 className="screen-title">Processing Status</h1>
            <p className="body-text text-secondary">
              Track your document processing and view results
            </p>
          </div>
          
          {/* Job Status Tabs */}
          <Card className="job-tabs-card">
            <div className="flex border-b border-gray-200">
              <button
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                  showJobStatus ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => {
                  setShowJobStatus(true);
                  setShowJobHistory(false);
                }}
              >
                Current Job
              </button>
              <button
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                  showJobHistory ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => {
                  setShowJobHistory(true);
                  setShowJobStatus(false);
                }}
              >
                History
              </button>
            </div>
            
            <div className="p-4">
              {showJobStatus && (jobId || lastJobId) && (
                <JobStatus 
                  jobId={jobId || lastJobId!}
                  onComplete={() => {
                    setShowJobStatus(false);
                    // Refresh transactions or navigate
                    window.location.reload();
                  }}
                  onError={() => {
                    setShowJobStatus(false);
                  }}
                />
              )}
              
              {showJobHistory && (
                <JobHistory 
                  userId="current-user" // Replace with actual user ID
                  onJobSelect={(selectedJobId) => {
                    setShowJobStatus(true);
                    setShowJobHistory(false);
                    // Update URL with selected job ID
                    const url = new URL(window.location.href);
                    url.searchParams.set('jobId', selectedJobId);
                    window.history.pushState({}, '', url.toString());
                  }}
                />
              )}
            </div>
          </Card>
          
          <div className="mt-4">
            <Button
              variant="primary"
              onClick={onNavigateToUpload}
              icon="upload"
            >
              Upload More Documents
            </Button>
          </div>
        </div>
      </section>
    );
  }

  // Show welcome screen when no data is uploaded
  if (transactions.length === 0) {
    return (
      <section id="screen-transactions" className="screen">
        <div className="welcome-container">
          <div className="welcome-header">
            <h1>Welcome to FinScope!</h1>
            <p>Get started by uploading your first bank statement.</p>
          </div>

          <NoDataEmptyState
            title="📊 No Financial Data Yet"
            description="Upload a bank statement or transaction receipt to see your financial insights here."
            onUpload={onNavigateToUpload}
            className="welcome-empty-state"
          />
        </div>
      </section>
    );
  }

  // Handler for auto-categorize action
  const handleAutoCategorize = async () => {
    setAutoCategorizeLoading(true);
    try {
      const response = await apiService.autoCategorizeTransactions(autoCategorizeSelection);
      if (response.success && response.data) {
        toast.success('Auto-categorization complete', `${response.data.length} transactions categorized.`);
        // Update the UI: update categories for affected transactions
        // This assumes transactions is managed in parent, so call onAutoCategorizeComplete if provided
        // Otherwise, reload or refetch transactions as needed
        setShowAutoCategorizeModal(false);
        setAutoCategorizeSelection([]);
        // Optionally, trigger a reload or inform parent to reload
        // For now, just reload the page (or you can refetch transactions via props)
        window.location.reload();
      } else {
        toast.error('Auto-categorization failed', response.error || 'Unknown error');
      }
    } catch (error: any) {
      toast.error('Auto-categorization failed', error.message || 'Unknown error');
    } finally {
      setAutoCategorizeLoading(false);
    }
  };

  // Handler for saving transaction edits
  const handleSaveEdit = async (transactionId: string) => {
    setEditLoading(transactionId);
    try {
      const updates = editValues[transactionId];
      if (!updates) return;
      const response = await apiService.updateTransaction(transactionId, updates);
      if (response.success && response.data) {
        toast.success('Transaction updated', 'Changes saved successfully.');
        setEditingId(null);
        setEditValues(v => { const nv = { ...v }; delete nv[transactionId]; return nv; });
        // Optionally, update the transaction in the UI directly:
        window.location.reload(); // Or refetch transactions if managed in parent
      } else {
        toast.error('Update failed', response.error || 'Unknown error');
      }
    } catch (error: any) {
      toast.error('Update failed', error.message || 'Unknown error');
    } finally {
      setEditLoading(null);
    }
  };

  return (
    <section id="screen-transactions" className="screen">
      {/* Controls Section */}
      <div className="transaction-controls">
        <div className="search-bar-container">
          <div className="search-input-wrapper">
            <Icon name="search" size={20} className="search-icon" />
            <input
              type="search"
              id="transaction-search-input"
              placeholder="Search by description or vendor..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="search-input"
            />
            {filters.search && (
              <button
                id="clear-search-button"
                onClick={clearSearch}
                className="clear-search-button"
                aria-label="Clear search"
              >
                <Icon name="x" size={16} />
              </button>
            )}
          </div>
        </div>

        <div className="filters">
          <select
            id="filter-month"
            value={filters.month}
            onChange={(e) => handleFilterChange('month', e.target.value)}
            className="filter-select"
          >
            <option value="">All Months</option>
            {monthOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          <select
            id="filter-category"
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className="filter-select"
          >
            <option value="">All Categories</option>
            {categoryOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Overflow menu button */}
      <div style={{ position: 'absolute', top: 16, right: 16 }}>
        <Button icon="more_vert" aria-label="More actions" onClick={() => setShowAutoCategorizeModal(true)} />
      </div>

      {/* Auto-categorize Modal */}
      <Modal isOpen={showAutoCategorizeModal} onClose={() => setShowAutoCategorizeModal(false)}>
        <h2>Auto-categorize Transactions</h2>
        <div>
          <label>Date Range:</label>
          <input type="date" value={autoCategorizeDateRange.start} onChange={e => setAutoCategorizeDateRange(v => ({...v, start: e.target.value}))} />
          <input type="date" value={autoCategorizeDateRange.end} onChange={e => setAutoCategorizeDateRange(v => ({...v, end: e.target.value}))} />
        </div>
        <div>
          <label>
            <input type="checkbox" checked={autoCategorizeSelection.length === filteredUncategorized.length && filteredUncategorized.length > 0}
              onChange={e => setAutoCategorizeSelection(e.target.checked ? filteredUncategorized.map(t => t.id) : [])} />
            Select All
          </label>
        </div>
        <ul style={{ maxHeight: 200, overflowY: 'auto' }}>
          {filteredUncategorized.map(t => (
            <li key={t.id}>
              <label>
                <input type="checkbox" checked={autoCategorizeSelection.includes(t.id)}
                  onChange={e => {
                    setAutoCategorizeSelection(sel => e.target.checked ? [...sel, t.id] : sel.filter(id => id !== t.id));
                  }} />
                {t.description} ({formatDate(t.date)}) - ₦{t.amount}
              </label>
            </li>
          ))}
        </ul>
        <Button onClick={handleAutoCategorize} disabled={autoCategorizeSelection.length === 0 || autoCategorizeLoading}>
          {autoCategorizeLoading ? 'Categorizing...' : 'Auto-categorize Selected'}
        </Button>
      </Modal>

      {/* Transaction List Card */}
      <div className="card">
        <h2>Transaction List</h2>

        {Object.keys(groupedTransactions).length === 0 ? (
          <div className="empty-state-container">
            <Icon name="search" size={32} className="text-secondary" />
            <p>No transactions found for the selected filters.</p>
          </div>
        ) : (
          <div className="transaction-list">
            {Object.entries(groupedTransactions).map(([date, dayTransactions]) => (
              <div key={date} className="transaction-date-group">
                <div className="transaction-date-group-header">
                  {formatDate(date)}
                </div>

                {dayTransactions.map((transaction) => (
                  <Card key={transaction.id}>
                    <CardHeader>
                      <div className="transaction-icon-container">
                        <Icon
                          name={getCategoryIcon(transaction.category || 'Uncategorized')}
                          size={16}
                          className="category-icon"
                        />
                      </div>

                      <div className="transaction-content">
                        <div className="transaction-description">
                          {transaction.description}
                        </div>
                        <div className="transaction-date-category">
                          {transaction.time && (
                            <>
                              {new Date(`2000-01-01T${transaction.time}`).toLocaleTimeString('en-US', {
                                hour: 'numeric',
                                minute: '2-digit',
                                hour12: true
                              })}
                              {transaction.category && ' • '}
                            </>
                          )}
                          {transaction.category}
                        </div>
                      </div>

                      <div className={`transaction-amount ${getAmountColorClass(transaction.type)}`}>
                        {formatAmount(transaction.amount, transaction.type)}
                      </div>
                      <Button icon="edit" onClick={() => {
                        setEditingId(transaction.id);
                        setEditValues({
                          ...editValues,
                          [transaction.id]: {
                            vendor: transaction.vendor,
                            notes: transaction.notes,
                            category: transaction.category
                          }
                        });
                      }} />
                    </CardHeader>
                    <CardBody>
                      {editingId === transaction.id ? (
                        <form onSubmit={e => { e.preventDefault(); handleSaveEdit(transaction.id); }}>
                          <Input
                            label="Vendor"
                            value={editValues[transaction.id]?.vendor || ''}
                            onChange={value => setEditValues(v => ({ ...v, [transaction.id]: { ...v[transaction.id], vendor: value } }))}
                          />
                          <Input
                            label="Notes"
                            value={editValues[transaction.id]?.notes || ''}
                            onChange={value => setEditValues(v => ({ ...v, [transaction.id]: { ...v[transaction.id], notes: value } }))}
                          />
                          <Select
                            label="Category"
                            value={editValues[transaction.id]?.category || ''}
                            options={categoryOptions}
                            onChange={value => setEditValues(v => ({ ...v, [transaction.id]: { ...v[transaction.id], category: value } }))}
                          />
                          <Button type="submit" disabled={editLoading === transaction.id}>
                            {editLoading === transaction.id ? 'Saving...' : 'Save'}
                          </Button>
                          <Button type="button" onClick={() => setEditingId(null)} variant="secondary">Cancel</Button>
                        </form>
                      ) : (
                        <>
                          <div>Vendor: {transaction.vendor}</div>
                          <div>Notes: {transaction.notes}</div>
                          <div>Category: {transaction.category}</div>
                        </>
                      )}
                    </CardBody>
                  </Card>
                ))}
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};
