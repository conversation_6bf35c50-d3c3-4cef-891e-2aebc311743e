import React from 'react';
import { Icon } from './Icon';

interface ButtonProps {
  children?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  iconPosition?: 'left' | 'right';
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  fullWidth?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  onClick,
  type = 'button',
  className = '',
  fullWidth = false
}) => {
  const buttonClasses = [
    'action-button',
    variant,
    size,
    fullWidth ? 'full-width' : '',
    loading ? 'loading' : '',
    className
  ].filter(Boolean).join(' ');

  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  const renderIcon = (position: 'left' | 'right') => {
    if (!icon || iconPosition !== position) return null;
    
    if (loading && position === 'left') {
      return <Icon name="loader" className="button-icon spinning" size={18} />;
    }
    
    return <Icon name={icon} className="button-icon" size={18} />;
  };

  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || loading}
    >
      {renderIcon('left')}
      <span className="button-text">{children}</span>
      {renderIcon('right')}
    </button>
  );
};

// Specialized button variants
interface IconButtonProps {
  icon: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  className?: string;
  ariaLabel?: string;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onClick,
  variant = 'ghost',
  size = 'medium',
  disabled = false,
  className = '',
  ariaLabel
}) => {
  const buttonClasses = [
    'icon-button',
    variant,
    size,
    className
  ].filter(Boolean).join(' ');

  const iconSize = size === 'small' ? 16 : size === 'large' ? 24 : 20;

  return (
    <button
      type="button"
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
    >
      <Icon name={icon} size={iconSize} />
    </button>
  );
};

interface LoadingButtonProps extends Omit<ButtonProps, 'loading'> {
  isLoading: boolean;
  loadingText?: string;
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  isLoading,
  loadingText = 'Loading...',
  children,
  ...props
}) => {
  return (
    <Button
      {...props}
      loading={isLoading}
      icon={isLoading ? 'loader' : props.icon}
    >
      {isLoading ? loadingText : children}
    </Button>
  );
};

interface FloatingActionButtonProps {
  icon: string;
  onClick?: () => void;
  className?: string;
  ariaLabel?: string;
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  icon,
  onClick,
  className = '',
  ariaLabel
}) => {
  return (
    <button
      type="button"
      className={`fab ${className}`}
      onClick={onClick}
      aria-label={ariaLabel}
    >
      <Icon name={icon} size={24} />
    </button>
  );
};
