import React, { useState, useRef, useCallback } from 'react';
import { apiService } from '../services/api';

interface DocumentUploadProps {
  onProcessingComplete: () => void;
}

interface ProcessingStatus {
  status: 'idle' | 'uploading' | 'processing' | 'success' | 'error';
  message: string;
  progress?: number;
}

export const DocumentUpload: React.FC<DocumentUploadProps> = ({ onProcessingComplete }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({
    status: 'idle',
    message: 'Drag and drop your document here or click to browse'
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, []);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setProcessingStatus({
        status: 'error',
        message: 'Invalid file type. Please upload JPEG, PNG, or WebP images.'
      });
      return;
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
      setProcessingStatus({
        status: 'error',
        message: 'File too large. Maximum size is 10MB.'
      });
      return;
    }

    setProcessingStatus({
      status: 'uploading',
      message: 'Uploading document...',
      progress: 0
    });

    try {
      // Create FormData
      const formData = new FormData();
      formData.append('document', file);

      setProcessingStatus({
        status: 'processing',
        message: 'Processing document with AI...',
        progress: 50
      });

      // Upload and process document
      const response = await apiService.processDocument(formData);

      if (response.success) {
        const transactionCount = response.data?.processingResult?.extractedData?.transactions?.length || 0;
        setProcessingStatus({
          status: 'success',
          message: `Successfully processed ${transactionCount} transactions!`,
          progress: 100
        });

        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }

        // Notify parent component
        onProcessingComplete();

        // Reset status after 3 seconds
        setTimeout(() => {
          setProcessingStatus({
            status: 'idle',
            message: 'Drag and drop your document here or click to browse'
          });
        }, 3000);
      } else {
        throw new Error(response.error || 'Processing failed');
      }
    } catch (error) {
      console.error('Document processing error:', error);
      setProcessingStatus({
        status: 'error',
        message: error instanceof Error ? error.message : 'Failed to process document'
      });
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const getStatusColor = () => {
    switch (processingStatus.status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'processing': return 'text-blue-600';
      case 'uploading': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getBorderColor = () => {
    if (isDragOver) return 'border-blue-500 bg-blue-50';
    switch (processingStatus.status) {
      case 'success': return 'border-green-500 bg-green-50';
      case 'error': return 'border-red-500 bg-red-50';
      case 'processing': return 'border-blue-500 bg-blue-50';
      case 'uploading': return 'border-yellow-500 bg-yellow-50';
      default: return 'border-gray-300 bg-white';
    }
  };

  return (
    <div className="document-upload">
      <h3 className="text-lg font-semibold mb-4">Upload Financial Document</h3>
      
      <div
        className={`upload-zone ${getBorderColor()} ${
          isDragOver ? 'scale-105' : ''
        } transition-all duration-200 ease-in-out`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg,image/png,image/webp"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="upload-content">
          <div className="upload-icon">
            {processingStatus.status === 'processing' && (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            )}
            {processingStatus.status === 'success' && (
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            )}
            {processingStatus.status === 'error' && (
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            )}
            {(processingStatus.status === 'idle' || processingStatus.status === 'uploading') && (
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            )}
          </div>
          
          <p className={`upload-message ${getStatusColor()}`}>
            {processingStatus.message}
          </p>
          
          {processingStatus.progress !== undefined && processingStatus.progress > 0 && (
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${processingStatus.progress}%` }}
              ></div>
            </div>
          )}
          
          <p className="upload-hint text-sm text-gray-500 mt-2">
            Supports: Receipts, Bank Statements, Transaction Slips
          </p>
        </div>
      </div>
      
      <div className="upload-features mt-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">What we extract:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Transaction amounts and dates</li>
          <li>• Merchant and vendor information</li>
          <li>• Account details and reference numbers</li>
          <li>• Transaction types and categories</li>
          <li>• Bank and payment method information</li>
        </ul>
      </div>
    </div>
  );
}; 