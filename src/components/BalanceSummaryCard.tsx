import React from 'react';
import { <PERSON>, CardHeader, CardBody } from './Card';
import { Icon } from './Icon';

interface BalanceItem {
  label: string;
  amount: number;
  type: 'income' | 'expense' | 'net';
  icon: string;
  trend?: {
    value: number;
    direction: 'up' | 'down';
    period: string;
  };
}

interface BalanceSummaryCardProps {
  totalIncome: number;
  totalExpenses: number;
  netCashFlow: number;
  previousPeriodData?: {
    totalIncome: number;
    totalExpenses: number;
    netCashFlow: number;
  };
  period?: string;
  className?: string;
}

export const BalanceSummaryCard: React.FC<BalanceSummaryCardProps> = ({
  totalIncome,
  totalExpenses,
  netCashFlow,
  previousPeriodData,
  period = 'this month',
  className = ''
}) => {
  const formatCurrency = (amount: number): string => {
    return `₦${Math.abs(amount).toLocaleString()}`;
  };

  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return null;
    const change = ((current - previous) / Math.abs(previous)) * 100;
    return {
      value: Math.abs(change),
      direction: change >= 0 ? 'up' : 'down' as 'up' | 'down',
      period: 'vs last period'
    };
  };

  const balanceItems: BalanceItem[] = [
    {
      label: 'Money In',
      amount: totalIncome,
      type: 'income',
      icon: 'trending-up',
      trend: previousPeriodData ? calculateTrend(totalIncome, previousPeriodData.totalIncome) || undefined : undefined
    },
    {
      label: 'Money Out',
      amount: totalExpenses,
      type: 'expense',
      icon: 'trending-down',
      trend: previousPeriodData ? calculateTrend(totalExpenses, previousPeriodData.totalExpenses) || undefined : undefined
    },
    {
      label: 'Net Cash Flow',
      amount: netCashFlow,
      type: 'net',
      icon: netCashFlow >= 0 ? 'plus-circle' : 'minus-circle',
      trend: previousPeriodData ? calculateTrend(netCashFlow, previousPeriodData.netCashFlow) || undefined : undefined
    }
  ];

  const getAmountColorClass = (type: BalanceItem['type'], amount: number): string => {
    switch (type) {
      case 'income':
        return 'text-credit';
      case 'expense':
        return 'text-debit';
      case 'net':
        return amount >= 0 ? 'text-credit' : 'text-debit';
      default:
        return 'text-primary';
    }
  };

  const getTrendColorClass = (direction: 'up' | 'down', type: BalanceItem['type']): string => {
    if (type === 'income') {
      return direction === 'up' ? 'text-credit' : 'text-debit';
    } else if (type === 'expense') {
      return direction === 'up' ? 'text-debit' : 'text-credit';
    } else { // net
      return direction === 'up' ? 'text-credit' : 'text-debit';
    }
  };

  return (
    <Card className={`balance-summary-card ${className}`}>
      <CardHeader>
        <h3 className="card-header">Balance Summary</h3>
        <p className="small-text text-secondary">Financial overview for {period}</p>
      </CardHeader>
      
      <CardBody>
        <div className="balance-items">
          {balanceItems.map((item, index) => (
            <div key={index} className="balance-item">
              <div className="balance-item-header">
                <div className="balance-item-icon">
                  <Icon 
                    name={item.icon} 
                    size={20} 
                    className={getAmountColorClass(item.type, item.amount)}
                  />
                </div>
                <div className="balance-item-info">
                  <span className="balance-item-label small-text-medium">
                    {item.label}
                  </span>
                  {item.trend && (
                    <div className="balance-item-trend">
                      <Icon 
                        name={item.trend.direction === 'up' ? 'arrow-up' : 'arrow-down'}
                        size={12}
                        className={getTrendColorClass(item.trend.direction, item.type)}
                      />
                      <span className={`trend-value ${getTrendColorClass(item.trend.direction, item.type)}`}>
                        {item.trend.value.toFixed(1)}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="balance-item-amount">
                <span className={`amount-value hero-amount ${getAmountColorClass(item.type, item.amount)}`}>
                  {item.type === 'expense' || (item.type === 'net' && item.amount < 0) ? '-' : ''}
                  {formatCurrency(item.amount)}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Summary insight */}
        <div className="balance-summary-insight">
          <div className="insight-content">
            <Icon 
              name={netCashFlow >= 0 ? 'check-circle' : 'alert-circle'} 
              size={16}
              className={netCashFlow >= 0 ? 'text-credit' : 'text-debit'}
            />
            <span className="small-text">
              {netCashFlow >= 0 
                ? `You saved ${formatCurrency(netCashFlow)} ${period}`
                : `You spent ${formatCurrency(Math.abs(netCashFlow))} more than you earned ${period}`
              }
            </span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};
