import React, { useMemo } from 'react';
import { Transaction } from '../types';
import { Icon } from './Icon';
import { Button } from './Button';

interface InsightsScreenProps {
  transactions: Transaction[];
  onNavigateToUpload?: () => void;
}

interface InsightData {
  id: string;
  title: string;
  description: string;
  icon: string;
  borderColor?: string;
  isPlaceholder?: boolean;
}

export const InsightsScreen: React.FC<InsightsScreenProps> = ({
  transactions,
  onNavigateToUpload
}) => {
  // Calculate insights from all transactions (all-time data)
  const insights = useMemo(() => {
    if (transactions.length === 0) return [];

    const insights: InsightData[] = [];

    // Calculate basic stats
    const debitTransactions = transactions.filter(t => t.type === 'debit');
    const creditTransactions = transactions.filter(t => t.type === 'credit');

    const totalIncome = creditTransactions.reduce((sum, t) => sum + t.amount, 0);

    // A. Top Spending Category Card
    const categoryBreakdown: Record<string, number> = {};
    debitTransactions.forEach(t => {
      const category = t.category || 'Uncategorized';
      categoryBreakdown[category] = (categoryBreakdown[category] || 0) + t.amount;
    });

    const topCategory = Object.entries(categoryBreakdown)
      .sort(([, a], [, b]) => b - a)[0];

    if (topCategory) {
      insights.push({
        id: 'top-spending',
        title: 'Top Spending Category',
        description: `Your biggest area of spending was ${topCategory[0]}, totaling <span class="highlight-amount">₦${topCategory[1].toLocaleString()}</span>.`,
        icon: 'award',
        borderColor: 'var(--primary-green)'
      });
    }

    // B. Largest Single Outflow Card
    const largestExpense = debitTransactions
      .sort((a, b) => b.amount - a.amount)[0];

    if (largestExpense) {
      insights.push({
        id: 'largest-expense',
        title: 'Largest Single Expense',
        description: `Your largest single transaction was for '${largestExpense.description}' at <span class="highlight-amount">₦${largestExpense.amount.toLocaleString()}</span>.`,
        icon: 'chevrons-down',
        borderColor: 'var(--primary-green)'
      });
    }

    // C. Income Snapshot Card
    if (creditTransactions.length > 0) {
      const largestIncome = creditTransactions
        .sort((a, b) => b.amount - a.amount)[0];

      // Count unique income sources (simplified by unique descriptions)
      const uniqueIncomeSources = new Set(
        creditTransactions.map(t => t.description.toLowerCase().trim())
      ).size;

      const incomeDescription = `
        Total income received: <span class="highlight-amount">₦${totalIncome.toLocaleString()}</span><br>
        Across <span class="highlight-amount">${creditTransactions.length}</span> separate transactions.<br>
        Your largest single income was from '${largestIncome.description}' for <span class="highlight-amount">₦${largestIncome.amount.toLocaleString()}</span>.<br>
        Estimated unique income sources: <span class="highlight-amount">${uniqueIncomeSources}</span>
      `;

      insights.push({
        id: 'income-snapshot',
        title: 'Income Snapshot',
        description: incomeDescription,
        icon: 'trending-up',
        borderColor: 'var(--primary-green)'
      });
    }

    // D. Placeholder / "Coming Soon" Card
    insights.push({
      id: 'coming-soon',
      title: 'More Insights Coming Soon!',
      description: 'Future updates will include features like recurring bill detection, monthly spending comparisons, and personalized budgeting goals.',
      icon: 'clock',
      borderColor: 'var(--accent-blue)',
      isPlaceholder: true
    });

    return insights;
  }, [transactions]);

  // Empty state when no transactions
  if (transactions.length === 0) {
    return (
      <section id="screen-insights" className="screen">
        <div className="insights-empty-state-container">
          <div className="insights-empty-state-content">
            <Icon name="bar-chart-2" size={64} className="text-secondary" />
            <h3>Unlock Your Financial Insights</h3>
            <p>Analyze your first statement to see a summary of your spending habits and income sources here.</p>
            {onNavigateToUpload && (
              <Button
                variant="primary"
                onClick={onNavigateToUpload}
                className="empty-state-cta"
              >
                Get Started
              </Button>
            )}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="screen-insights" className="screen">
      <div className="insights-list">
        {insights.map((insight) => (
          <div
            key={insight.id}
            className={`insight-card ${insight.isPlaceholder ? 'placeholder-card' : ''}`}
            style={{ borderLeft: `4px solid ${insight.borderColor}` }}
          >
            <div className="insight-card-icon-container">
              <Icon
                name={insight.icon}
                size={20}
                color={insight.isPlaceholder ? 'var(--accent-blue)' : 'var(--primary-green)'}
              />
            </div>
            <div className="insight-card-content">
              <h3>{insight.title}</h3>
              <p dangerouslySetInnerHTML={{ __html: insight.description }} />
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};
