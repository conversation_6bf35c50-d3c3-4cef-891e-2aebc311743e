import React from 'react';
import { ChartDataItem } from './DonutChart';
import { Icon } from './Icon';

interface ChartLegendProps {
  data: ChartDataItem[];
  onItemClick?: (item: ChartDataItem) => void;
  onItemHover?: (item: ChartDataItem | null) => void;
  selectedItems?: string[];
  className?: string;
  variant?: 'default' | 'compact';
  showPercentages?: boolean;
  showAmounts?: boolean;
  maxItems?: number;
}

interface LegendItemProps {
  item: ChartDataItem;
  isSelected?: boolean;
  onClick?: (item: ChartDataItem) => void;
  onHover?: (item: ChartDataItem | null) => void;
  variant?: 'default' | 'compact';
  showPercentage?: boolean;
  showAmount?: boolean;
}

const LegendItem: React.FC<LegendItemProps> = ({
  item,
  isSelected = false,
  onClick,
  onHover,
  variant = 'default',
  showPercentage = true,
  showAmount = true
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(item);
    }
  };

  const handleMouseEnter = () => {
    if (onHover) {
      onHover(item);
    }
  };

  const handleMouseLeave = () => {
    if (onHover) {
      onHover(null);
    }
  };

  const itemClasses = [
    'legend-item',
    variant,
    isSelected ? 'selected' : '',
    onClick ? 'clickable' : ''
  ].filter(Boolean).join(' ');

  return (
    <div
      className={itemClasses}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="legend-item-indicator">
        <div 
          className="legend-color-dot"
          style={{ backgroundColor: item.color }}
        />
        {isSelected && (
          <Icon name="check" size={12} className="legend-check-icon" />
        )}
      </div>
      
      <div className="legend-item-content">
        <div className="legend-item-header">
          <span className="legend-category">{item.category}</span>
          {showPercentage && (
            <span className="legend-percentage">
              {item.percentage.toFixed(1)}%
            </span>
          )}
        </div>
        
        {showAmount && (
          <div className="legend-item-amount">
            <span className="legend-amount-value">
              ₦{item.amount.toLocaleString()}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export const ChartLegend: React.FC<ChartLegendProps> = ({
  data,
  onItemClick,
  onItemHover,
  selectedItems = [],
  className = '',
  variant = 'default',
  showPercentages = true,
  showAmounts = true,
  maxItems
}) => {
  const displayData = maxItems ? data.slice(0, maxItems) : data;
  const hasMoreItems = maxItems && data.length > maxItems;
  const remainingItems = hasMoreItems ? data.slice(maxItems) : [];
  const remainingTotal = remainingItems.reduce((sum, item) => sum + item.amount, 0);
  const remainingPercentage = remainingItems.reduce((sum, item) => sum + item.percentage, 0);

  const legendClasses = [
    'chart-legend',
    variant,
    className
  ].filter(Boolean).join(' ');

  if (data.length === 0) {
    return (
      <div className={`${legendClasses} empty`}>
        <div className="legend-empty-state">
          <Icon name="pie-chart" size={24} className="text-secondary" />
          <span className="small-text text-secondary">No categories to display</span>
        </div>
      </div>
    );
  }

  return (
    <div className={legendClasses}>
      <div className="legend-items">
        {displayData.map((item, index) => (
          <LegendItem
            key={`${item.category}-${index}`}
            item={item}
            isSelected={selectedItems.includes(item.category)}
            onClick={onItemClick}
            onHover={onItemHover}
            variant={variant}
            showPercentage={showPercentages}
            showAmount={showAmounts}
          />
        ))}
        
        {hasMoreItems && (
          <div className="legend-item others">
            <div className="legend-item-indicator">
              <div className="legend-color-dot others-dot" />
            </div>
            <div className="legend-item-content">
              <div className="legend-item-header">
                <span className="legend-category">
                  Others ({remainingItems.length} categories)
                </span>
                {showPercentages && (
                  <span className="legend-percentage">
                    {remainingPercentage.toFixed(1)}%
                  </span>
                )}
              </div>
              {showAmounts && (
                <div className="legend-item-amount">
                  <span className="legend-amount-value">
                    ₦{remainingTotal.toLocaleString()}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {onItemClick && selectedItems.length > 0 && (
        <div className="legend-actions">
          <button 
            className="action-button outline small"
            onClick={() => {
              // Clear all selections by calling onItemClick with each selected item
              selectedItems.forEach(category => {
                const item = data.find(d => d.category === category);
                if (item && onItemClick) {
                  onItemClick(item);
                }
              });
            }}
          >
            Clear Selection
          </button>
        </div>
      )}
    </div>
  );
};

// Hook for managing legend selection state
export const useLegendSelection = (initialSelection: string[] = []) => {
  const [selectedItems, setSelectedItems] = React.useState<string[]>(initialSelection);

  const toggleItem = (category: string) => {
    setSelectedItems(prev => 
      prev.includes(category)
        ? prev.filter(item => item !== category)
        : [...prev, category]
    );
  };

  const selectItem = (category: string) => {
    setSelectedItems(prev => 
      prev.includes(category) ? prev : [...prev, category]
    );
  };

  const deselectItem = (category: string) => {
    setSelectedItems(prev => prev.filter(item => item !== category));
  };

  const clearSelection = () => {
    setSelectedItems([]);
  };

  const selectAll = (categories: string[]) => {
    setSelectedItems(categories);
  };

  const handleItemClick = (item: ChartDataItem) => {
    toggleItem(item.category);
  };

  return {
    selectedItems,
    toggleItem,
    selectItem,
    deselectItem,
    clearSelection,
    selectAll,
    handleItemClick,
    setSelectedItems
  };
};
