import React from 'react';
import { Icon } from './Icon';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  color,
  className = ''
}) => {
  const sizeMap = {
    small: 16,
    medium: 24,
    large: 32
  };

  return (
    <div className={`loading-spinner ${size} ${className}`}>
      <Icon 
        name="loader" 
        size={sizeMap[size]} 
        color={color}
        className="spinning"
      />
    </div>
  );
};

interface LoadingBarProps {
  progress?: number;
  indeterminate?: boolean;
  className?: string;
  height?: number;
}

export const LoadingBar: React.FC<LoadingBarProps> = ({
  progress = 0,
  indeterminate = false,
  className = '',
  height = 3
}) => {
  return (
    <div 
      className={`loading-bar ${indeterminate ? 'indeterminate' : ''} ${className}`}
      style={{ height: `${height}px` }}
    >
      <div 
        className="loading-bar-fill"
        style={{ 
          width: indeterminate ? '100%' : `${Math.min(100, Math.max(0, progress))}%` 
        }}
      />
    </div>
  );
};

interface GlobalLoadingBarProps {
  isVisible: boolean;
  progress?: number;
}

export const GlobalLoadingBar: React.FC<GlobalLoadingBarProps> = ({
  isVisible,
  progress
}) => {
  if (!isVisible) return null;

  return (
    <div className="global-loading-bar">
      <LoadingBar 
        progress={progress} 
        indeterminate={progress === undefined}
        height={3}
      />
    </div>
  );
};

interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  borderRadius?: string | number;
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  borderRadius,
  className = '',
  variant = 'text'
}) => {
  const getDefaultBorderRadius = () => {
    switch (variant) {
      case 'circular':
        return '50%';
      case 'rectangular':
        return '4px';
      case 'text':
        return '4px';
      default:
        return '4px';
    }
  };

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
    borderRadius: borderRadius || getDefaultBorderRadius()
  };

  return (
    <div 
      className={`skeleton ${variant} ${className}`}
      style={style}
    />
  );
};

interface SkeletonCardProps {
  lines?: number;
  showAvatar?: boolean;
  className?: string;
}

export const SkeletonCard: React.FC<SkeletonCardProps> = ({
  lines = 3,
  showAvatar = false,
  className = ''
}) => {
  return (
    <div className={`skeleton-card ${className}`}>
      {showAvatar && (
        <div className="skeleton-card-header">
          <Skeleton variant="circular" width={40} height={40} />
          <div className="skeleton-card-header-text">
            <Skeleton width="60%" height="1rem" />
            <Skeleton width="40%" height="0.875rem" />
          </div>
        </div>
      )}
      <div className="skeleton-card-content">
        {Array.from({ length: lines }).map((_, index) => (
          <Skeleton 
            key={index}
            width={index === lines - 1 ? '70%' : '100%'}
            height="1rem"
            className="skeleton-line"
          />
        ))}
      </div>
    </div>
  );
};

interface SkeletonListProps {
  items?: number;
  showAvatar?: boolean;
  className?: string;
}

export const SkeletonList: React.FC<SkeletonListProps> = ({
  items = 5,
  showAvatar = false,
  className = ''
}) => {
  return (
    <div className={`skeleton-list ${className}`}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="skeleton-list-item">
          {showAvatar && (
            <Skeleton variant="circular" width={32} height={32} />
          )}
          <div className="skeleton-list-item-content">
            <Skeleton width="70%" height="1rem" />
            <Skeleton width="50%" height="0.875rem" />
          </div>
          <Skeleton width="60px" height="1rem" />
        </div>
      ))}
    </div>
  );
};

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  children?: React.ReactNode;
  className?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message = 'Loading...',
  children,
  className = ''
}) => {
  if (!isVisible) return null;

  return (
    <div className={`loading-overlay ${className}`}>
      <div className="loading-overlay-content">
        <LoadingSpinner size="large" />
        {message && <p className="loading-overlay-message">{message}</p>}
        {children}
      </div>
    </div>
  );
};

interface PageLoadingProps {
  message?: string;
  className?: string;
}

export const PageLoading: React.FC<PageLoadingProps> = ({
  message = 'Loading...',
  className = ''
}) => {
  return (
    <div className={`page-loading ${className}`}>
      <LoadingSpinner size="large" />
      <p className="page-loading-message">{message}</p>
    </div>
  );
};

// Hook for managing loading states
export const useLoading = (initialState = false) => {
  const [isLoading, setIsLoading] = React.useState(initialState);

  const startLoading = () => setIsLoading(true);
  const stopLoading = () => setIsLoading(false);
  const toggleLoading = () => setIsLoading(prev => !prev);

  return {
    isLoading,
    startLoading,
    stopLoading,
    toggleLoading,
    setIsLoading
  };
};
