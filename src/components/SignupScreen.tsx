import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardBody } from './Card';
import { Input } from './Input';
import { Button } from './Button';
import { useToastContext } from './Toast';
import { authService } from '../services/auth';

interface SignupScreenProps {
  onSignUpSuccess: () => void;
  onNavigateToSignIn: () => void;
}

export const SignupScreen: React.FC<SignupScreenProps> = ({ onSignUpSuccess, onNavigateToSignIn }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string; confirmPassword?: string }>({});
  const toast = useToastContext();

  const validate = () => {
    const errs: typeof errors = {};
    if (!email) {
      errs.email = 'Email is required';
    } else if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      errs.email = 'Invalid email address';
    }
    if (!password) {
      errs.password = 'Password is required';
    } else if (password.length < 6) {
      errs.password = 'Password must be at least 6 characters';
    }
    if (!confirmPassword) {
      errs.confirmPassword = 'Please confirm your password';
    } else if (password !== confirmPassword) {
      errs.confirmPassword = 'Passwords do not match';
    }
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) return;
    setLoading(true);
    try {
      await authService.signUpWithEmail(email, password);
      toast.success('Sign Up Successful', 'Your account has been created. You can now sign in.');
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      onSignUpSuccess();
    } catch (error: any) {
      let message = 'Sign up failed. Please try again.';
      if (error.code === 'auth/email-already-in-use') {
        message = 'This email is already in use.';
      } else if (error.code === 'auth/invalid-email') {
        message = 'Invalid email address.';
      } else if (error.code === 'auth/weak-password') {
        message = 'Password is too weak.';
      }
      toast.error('Sign Up Failed', message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-screen">
      <Card className="auth-card">
        <CardHeader>
          <h2 className="card-header">Create an Account</h2>
        </CardHeader>
        <CardBody>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              label="Email"
              type="email"
              value={email}
              onChange={setEmail}
              error={errors.email}
              autoComplete="email"
              required
            />
            <Input
              label="Password"
              type="password"
              value={password}
              onChange={setPassword}
              error={errors.password}
              autoComplete="new-password"
              required
            />
            <Input
              label="Confirm Password"
              type="password"
              value={confirmPassword}
              onChange={setConfirmPassword}
              error={errors.confirmPassword}
              autoComplete="new-password"
              required
            />
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
              className="w-full"
            >
              Sign Up
            </Button>
          </form>
          <div className="mt-4 text-center text-sm text-secondary">
            Already have an account?{' '}
            <button
              type="button"
              className="text-blue-600 hover:underline font-medium"
              onClick={onNavigateToSignIn}
              disabled={loading}
            >
              Sign in
            </button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}; 