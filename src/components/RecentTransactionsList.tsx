import React from 'react';
import { Card, CardHeader, CardBody } from './Card';
import { Icon, getCategoryIcon } from './Icon';
import { Button } from './Button';

interface Transaction {
  id: string;
  date: string;
  time: string | null;
  description: string;
  amount: number;
  type: 'credit' | 'debit';
  category?: string;
  vendor?: string;
}

interface RecentTransactionsListProps {
  transactions: Transaction[];
  maxItems?: number;
  onViewAll?: () => void;
  onTransactionClick?: (transaction: Transaction) => void;
  className?: string;
  showHeader?: boolean;
  variant?: 'default' | 'compact';
}

interface TransactionItemProps {
  transaction: Transaction;
  onClick?: (transaction: Transaction) => void;
  variant?: 'default' | 'compact';
}

const TransactionItem: React.FC<TransactionItemProps> = ({
  transaction,
  onClick,
  variant = 'default'
}) => {
  const formatDate = (dateString: string, timeString?: string | null) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const isToday = date.toDateString() === today.toDateString();
    const isYesterday = date.toDateString() === yesterday.toDateString();

    let dateLabel = '';
    if (isToday) {
      dateLabel = 'Today';
    } else if (isYesterday) {
      dateLabel = 'Yesterday';
    } else {
      dateLabel = date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
    }

    if (timeString && variant !== 'compact') {
      const time = new Date(`2000-01-01T${timeString}`);
      const timeLabel = time.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
      return `${dateLabel}, ${timeLabel}`;
    }

    return dateLabel;
  };

  const formatAmount = (amount: number, type: 'credit' | 'debit') => {
    const sign = type === 'credit' ? '+' : '-';
    return `${sign}₦${amount.toLocaleString()}`;
  };

  const getAmountColorClass = (type: 'credit' | 'debit') => {
    return type === 'credit' ? 'text-credit' : 'text-debit';
  };

  const handleClick = () => {
    if (onClick) {
      onClick(transaction);
    }
  };

  const itemClasses = [
    'transaction-item',
    variant,
    onClick ? 'clickable' : ''
  ].filter(Boolean).join(' ');

  const categoryIcon = transaction.category ? getCategoryIcon(transaction.category) : 'circle';

  return (
    <div className={itemClasses} onClick={handleClick}>
      <div className="transaction-icon">
        <Icon 
          name={categoryIcon} 
          size={variant === 'compact' ? 18 : 20}
          className={`category-icon ${getAmountColorClass(transaction.type)}`}
        />
      </div>
      
      <div className="transaction-content">
        <div className="transaction-main">
          <div className="transaction-description">
            <span className="description-text">{transaction.description}</span>
            {transaction.vendor && variant !== 'compact' && (
              <span className="vendor-text">at {transaction.vendor}</span>
            )}
          </div>
          <div className="transaction-amount">
            <span className={`amount-text ${getAmountColorClass(transaction.type)}`}>
              {formatAmount(transaction.amount, transaction.type)}
            </span>
          </div>
        </div>
        
        <div className="transaction-meta">
          <div className="transaction-date">
            <span className="date-text">
              {formatDate(transaction.date, transaction.time)}
            </span>
          </div>
          {transaction.category && (
            <div className="transaction-category">
              <span className="category-text">{transaction.category}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const RecentTransactionsList: React.FC<RecentTransactionsListProps> = ({
  transactions,
  maxItems = 5,
  onViewAll,
  onTransactionClick,
  className = '',
  showHeader = true,
  variant = 'default'
}) => {
  const displayTransactions = transactions.slice(0, maxItems);
  const hasMoreTransactions = transactions.length > maxItems;

  const cardClasses = [
    'recent-transactions-card',
    variant,
    className
  ].filter(Boolean).join(' ');

  if (transactions.length === 0) {
    return (
      <Card className={cardClasses}>
        {showHeader && (
          <CardHeader>
            <h3 className="card-header">Recent Transactions</h3>
          </CardHeader>
        )}
        <CardBody>
          <div className="transactions-empty-state">
            <Icon name="credit-card" size={32} className="text-secondary" />
            <span className="body-text text-secondary">No transactions yet</span>
            <p className="small-text text-secondary">
              Upload a bank statement to see your transactions here
            </p>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className={cardClasses}>
      {showHeader && (
        <CardHeader>
          <div className="transactions-header">
            <h3 className="card-header">Recent Transactions</h3>
            {hasMoreTransactions && onViewAll && (
              <Button
                variant="outline"
                size="small"
                onClick={onViewAll}
                icon="arrow-right"
                iconPosition="right"
              >
                View All
              </Button>
            )}
          </div>
        </CardHeader>
      )}
      
      <CardBody>
        <div className="transactions-list">
          {displayTransactions.map((transaction, index) => (
            <TransactionItem
              key={transaction.id || `transaction-${index}`}
              transaction={transaction}
              onClick={onTransactionClick}
              variant={variant}
            />
          ))}
        </div>
        
        {hasMoreTransactions && !onViewAll && (
          <div className="transactions-footer">
            <p className="small-text text-secondary">
              Showing {maxItems} of {transactions.length} transactions
            </p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

// Hook for managing transaction list state
export const useTransactionsList = (transactions: Transaction[]) => {
  const [selectedTransaction, setSelectedTransaction] = React.useState<Transaction | null>(null);

  const handleTransactionClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
  };

  const clearSelection = () => {
    setSelectedTransaction(null);
  };

  const getTransactionsByDateRange = (startDate: Date, endDate: Date) => {
    return transactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);
      return transactionDate >= startDate && transactionDate <= endDate;
    });
  };

  const getTransactionsByCategory = (category: string) => {
    return transactions.filter(transaction => transaction.category === category);
  };

  const getTransactionsByType = (type: 'credit' | 'debit') => {
    return transactions.filter(transaction => transaction.type === type);
  };

  return {
    selectedTransaction,
    handleTransactionClick,
    clearSelection,
    getTransactionsByDateRange,
    getTransactionsByCategory,
    getTransactionsByType,
    setSelectedTransaction
  };
};
