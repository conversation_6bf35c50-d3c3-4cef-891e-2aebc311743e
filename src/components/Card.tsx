import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hover?: boolean;
  style?: React.CSSProperties;
}

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface CardBodyProps {
  children: React.ReactNode;
  className?: string;
}

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const Card: React.FC<CardProps> = ({
  children,
  className = '',
  onClick,
  hover = false,
  style
}) => {
  const cardClasses = [
    'card',
    hover ? 'card-hover' : '',
    onClick ? 'card-clickable' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={cardClasses} onClick={onClick} style={style}>
      {children}
    </div>
  );
};

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '' }) => {
  return (
    <div className={`card-header ${className}`}>
      {children}
    </div>
  );
};

export const CardBody: React.FC<CardBodyProps> = ({ children, className = '' }) => {
  return (
    <div className={`card-body ${className}`}>
      {children}
    </div>
  );
};

export const CardFooter: React.FC<CardFooterProps> = ({ children, className = '' }) => {
  return (
    <div className={`card-footer ${className}`}>
      {children}
    </div>
  );
};

// Specialized card variants
interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  className?: string;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  className = ''
}) => {
  const trendClass = trend ? `stat-card-${trend}` : '';
  
  return (
    <Card className={`stat-card ${trendClass} ${className}`}>
      <div className="stat-card-content">
        {icon && <div className="stat-card-icon">{icon}</div>}
        <div className="stat-card-info">
          <h3 className="stat-card-title small-text-medium">{title}</h3>
          <p className="stat-card-value hero-amount">{value}</p>
          {subtitle && <p className="stat-card-subtitle small-text">{subtitle}</p>}
        </div>
      </div>
    </Card>
  );
};

interface InsightCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  borderColor?: string;
  className?: string;
}

export const InsightCard: React.FC<InsightCardProps> = ({
  title,
  description,
  icon,
  borderColor = 'var(--primary-green)',
  className = ''
}) => {
  return (
    <Card className={`insight-card ${className}`} style={{ borderLeft: `4px solid ${borderColor}` }}>
      <div className="insight-card-content">
        {icon && <div className="insight-card-icon">{icon}</div>}
        <div className="insight-card-info">
          <h3 className="insight-card-title card-header">{title}</h3>
          <p className="insight-card-description body-text">{description}</p>
        </div>
      </div>
    </Card>
  );
};
