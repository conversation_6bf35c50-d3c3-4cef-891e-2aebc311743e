import React, { useState, useRef } from 'react';
import { apiService } from '../services/api';
import { Card, CardHeader, CardBody } from './Card';
import { Button } from './Button';
import { Textarea } from './Input';
import { Icon } from './Icon';
import { useToastContext } from './Toast';

interface UploadScreenProps {
  onAnalysisComplete: () => void;
}

export const UploadScreen: React.FC<UploadScreenProps> = ({ onAnalysisComplete }) => {
  const [activeTab, setActiveTab] = useState<'file' | 'text'>('file');
  const [text, setText] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToastContext();

  // Change handleFileSelect to accept File or event
  const handleFileSelect = (input: React.ChangeEvent<HTMLInputElement> | File) => {
    let file: File | undefined;
    if (input instanceof File) {
      file = input;
    } else {
      file = input.target.files?.[0];
    }
    if (file) {
      // Validate file type
      const validTypes = [
        'image/png', 'image/jpeg', 'image/jpg', 
        'application/pdf',
        'text/csv', 'application/vnd.ms-excel', 
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      if (!validTypes.includes(file.type)) {
        toast.error('Invalid File Type', 'Please select a PNG, JPG, JPEG, PDF, CSV, or Excel file.');
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        toast.error('File Too Large', 'Please select a file smaller than 10MB.');
        return;
      }

      setSelectedFile(file);
      toast.success('File Selected', `${file.name} is ready for analysis.`);
    } else {
      setSelectedFile(null);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
  };

  // Update handleDrop to call handleFileSelect with File directly
  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleAnalysis = async (analysisMode: 'basic' | 'advanced') => {
    // Validate input based on active tab
    if (activeTab === 'text' && !text.trim()) {
      toast.error('No Text Provided', 'Please enter some text to analyze.');
      return;
    }

    if (activeTab === 'file' && !selectedFile) {
      toast.error('No File Selected', 'Please select a file to analyze.');
      return;
    }

    setIsProcessing(true);
    const analysisType = activeTab === 'file' ? 'document' : 'text';

    toast.info(
      'Processing Started',
      `${analysisMode === 'basic' ? 'Basic' : 'Advanced'} ${analysisType} processing in progress...`
    );

    try {
      let result;
      if (activeTab === 'text') {
        result = await apiService.analyzeText(text, analysisMode);
        
        const transactionCount = result?.transactions?.length || 0;
        
        toast.success(
          'Processing Complete!',
          `Successfully processed ${transactionCount} transactions using ${analysisMode} analysis.`
        );

        // Clear inputs after successful analysis
        setText('');
        onAnalysisComplete();
      } else {
        // Use async document processing
        const formData = new FormData();
        formData.append('document', selectedFile!);
        formData.append('analysisMode', analysisMode);
        result = await apiService.processDocument(formData);

        if (result.success) {
          const jobId = result.data.job_id || result.data.jobId; // Support both old and new schema

          toast.success(
            'Upload Successful!',
            'Document uploaded and queued for processing. You can leave and come back later to check the status.'
          );

          // Store job ID for tracking
          localStorage.setItem('lastJobId', jobId);

          // Clear file input
          setSelectedFile(null);
          if (fileInputRef.current) {
            fileInputRef.current.value = '';
          }

          // Navigate to job status or transactions screen
          setTimeout(() => {
            window.location.href = `/transactions?jobId=${jobId}`;
          }, 2000);
        } else {
          throw new Error(result.error || 'Upload failed');
        }
      }
    } catch (error) {
      console.error('Processing failed:', error);
      toast.error(
        'Processing Failed',
        `${analysisMode === 'basic' ? 'Basic' : 'Advanced'} processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const isAnalysisDisabled = () => {
    if (isProcessing) return true;
    if (activeTab === 'text') return !text.trim();
    if (activeTab === 'file') return !selectedFile;
    return true;
  };

  return (
    <div className="upload-screen">
      <div className="upload-header">
        <h1 className="screen-title">Upload & Analyze</h1>
        <p className="body-text text-secondary">
          Upload bank statements or paste transaction text for AI-powered analysis
        </p>
      </div>

      {/* Tabbed Upload Interface */}
      <Card className="upload-tabs-card">
        <div className="upload-tabs">
          <div className="tab-headers">
            <button
              className={`tab-header ${activeTab === 'file' ? 'active' : ''}`}
              onClick={() => setActiveTab('file')}
              disabled={isProcessing}
            >
              <Icon name="upload" size={18} />
              <span>Upload File</span>
            </button>
            <button
              className={`tab-header ${activeTab === 'text' ? 'active' : ''}`}
              onClick={() => setActiveTab('text')}
              disabled={isProcessing}
            >
              <Icon name="edit-3" size={18} />
              <span>Paste Text</span>
            </button>
          </div>

          <div className="tab-content">
            {activeTab === 'file' && (
              <div className="upload-tab">
                <div
                  className={`upload-zone ${dragOver ? 'drag-over' : ''} ${selectedFile ? 'has-file' : ''}`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    id="file-input"
                    accept=".png,.jpg,.jpeg,.pdf,.csv,.xls,.xlsx"
                    onChange={(e) => handleFileSelect(e)}
                    disabled={isProcessing}
                    className="file-input"
                  />
                  <label htmlFor="file-input" className="file-input-label">
                    <div className="upload-content">
                      {selectedFile ? (
                        <>
                          <Icon name="check-circle" size={48} className="upload-icon success" />
                          <div className="upload-text">
                            <span className="file-selected">{selectedFile.name}</span>
                            <span className="file-size">
                              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                            </span>
                          </div>
                          <Button
                            variant="outline"
                            size="small"
                            onClick={() => {
                              setSelectedFile(null);
                              if (fileInputRef.current) {
                                fileInputRef.current.value = '';
                              }
                            }}
                          >
                            Remove
                          </Button>
                        </>
                      ) : (
                        <>
                          <Icon name="upload-cloud" size={48} className="upload-icon" />
                          <div className="upload-text">
                            <span className="upload-message">
                              {dragOver ? 'Drop your file here' : 'Click to select or drag and drop'}
                            </span>
                            <span className="upload-hint">
                              Supports: PNG, JPG, JPEG, PDF, CSV, Excel files (max 10MB)
                            </span>
                          </div>
                        </>
                      )}
                    </div>
                  </label>
                </div>
              </div>
            )}

            {activeTab === 'text' && (
              <div className="text-tab">
                <Textarea
                  value={text}
                  onChange={setText}
                  placeholder="Paste your transaction text here...

Example:
Date: 2024-01-15
Time: 14:30:00
Description: Transfer to John Doe
Amount: 5000.00
Type: debit
Reference: TXN123456789"
                  rows={12}
                  disabled={isProcessing}
                  className="text-input"
                />
                <div className="text-counter">
                  <span className="small-text text-secondary">
                    {text.length} characters
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Analysis Mode Selection */}
      <Card className="analysis-section">
        <CardHeader>
          <div className="analysis-header">
            <h3 className="card-header">Choose Analysis Mode</h3>
            <Button
              variant="outline"
              size="small"
              icon={showHelp ? "chevron-up" : "info"}
              onClick={() => {
                if (showHelp) {
                  setIsClosing(true);
                  setTimeout(() => {
                    setShowHelp(false);
                    setIsClosing(false);
                  }, 300); // Match animation duration
                } else {
                  setShowHelp(true);
                }
              }}
            >
              {showHelp ? 'Hide Help' : 'Learn More'}
            </Button>
          </div>
        </CardHeader>

        <CardBody>
          {showHelp && (
            <div className={`help-panel ${isClosing ? 'closing' : ''}`}>
              <div className="help-item">
                <div className="help-header">
                  <Icon name="zap" size={20} className="text-primary" />
                  <span className="help-mode">Basic Analysis</span>
                </div>
                <p className="help-description">
                  Fast extraction of core transaction fields (date, time, description, amount, type, bank ID, source bank, sender/receiver names).
                  Optimized for speed - perfect for quick processing.
                </p>
              </div>
              <div className="help-item">
                <div className="help-header">
                  <Icon name="search" size={20} className="text-primary" />
                  <span className="help-mode">Advanced Analysis</span>
                </div>
                <p className="help-description">
                  Comprehensive extraction including all optional fields (parties involved, fees, merchant details, etc.)
                  plus automatic categorization. More detailed but slower processing.
                </p>
              </div>
            </div>
          )}

          <div className="analysis-buttons">
            <Button
              onClick={() => handleAnalysis('basic')}
              disabled={isAnalysisDisabled()}
              loading={isProcessing}
              icon="zap"
              variant="primary"
              size="large"
              className="analysis-button"
            >
              Basic Analysis
            </Button>
            <Button
              onClick={() => handleAnalysis('advanced')}
              disabled={isAnalysisDisabled()}
              loading={isProcessing}
              icon="search"
              variant="primary"
              size="large"
              className="analysis-button"
            >
              Advanced Analysis
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}; 