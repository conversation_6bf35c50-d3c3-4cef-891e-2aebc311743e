/* Global Layout Structure */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--light-tint-grey);
}

/* Fixed Header */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background-color: var(--card-bg);
  padding: 0 24px;
  box-shadow: var(--card-shadow);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.app-header h1 {
  color: var(--dark-charcoal-green);
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info span {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.sign-out-button {
  background-color: var(--semantic-error);
  color: var(--text-color-on-primary);
  border: none;
  padding: 8px 16px;
  border-radius: var(--border-radius-small);
  cursor: pointer;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  transition: background-color 0.2s ease;
}

.sign-out-button:hover {
  background-color: #c82333;
}

/* Main Content Area */
.app-main {
  flex: 1;
  margin-top: var(--header-height);
  margin-bottom: var(--bottom-nav-height);
  padding: 24px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  overflow-y: auto;
}

/* Fixed Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottom-nav-height);
  background-color: var(--card-bg);
  box-shadow: 0 -4px 12px rgba(0,0,0,0.08);
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 100;
  padding: 0 12px;
}

.nav-button {
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: var(--border-radius-small);
  cursor: pointer;
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-secondary);
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 60px;
}

.nav-button:hover {
  background-color: var(--primary-green-light-tint);
  color: var(--primary-green);
}

.nav-button.active {
  background-color: var(--primary-green-light-tint);
  color: var(--primary-green);
}

.nav-button-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2px;
}

.nav-button-label {
  font-size: var(--font-size-small);
  line-height: 1;
}

/* Navigation badges and notifications */
.nav-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: var(--semantic-error);
  color: var(--text-color-on-primary);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.nav-notification-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background-color: var(--semantic-error);
  border-radius: 50%;
  border: 2px solid var(--card-bg);
}

.nav-button.has-notification {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Enhanced navigation styles */
.bottom-nav.enhanced .nav-button {
  transition: all 0.3s ease;
}

.bottom-nav.enhanced .nav-button:hover {
  transform: translateY(-2px);
}

.bottom-nav.enhanced .nav-button.active {
  transform: translateY(-1px);
}

/* Responsive navigation */
@media (max-width: 480px) {
  .nav-button {
    min-width: 50px;
    padding: 6px 8px;
  }

  .nav-button-label {
    font-size: 11px;
  }

  .nav-badge {
    font-size: 9px;
    padding: 1px 4px;
    min-width: 14px;
    height: 14px;
  }
}

/* Enhanced Auth Screen */
.auth-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 24px;
  background: linear-gradient(135deg, var(--primary-green-extra-light-tint) 0%, var(--light-tint-grey) 100%);
}

.auth-container {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.auth-branding {
  text-align: center;
}

.auth-branding h1 {
  margin-bottom: 12px;
  color: var(--dark-charcoal-green);
}

.auth-branding p {
  max-width: 300px;
  margin: 0 auto;
  line-height: 1.5;
}

.auth-card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color-lighter);
}

.auth-card .card-header {
  text-align: center;
  border-bottom: none;
  margin-bottom: 24px;
}

.auth-card .card-header h2 {
  margin-bottom: 8px;
}

.google-signin-button {
  margin-bottom: 24px;
}

.auth-features {
  display: flex;
  justify-content: space-around;
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color-lighter);
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.feature-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.auth-footer {
  text-align: center;
}

.auth-footer p {
  max-width: 320px;
  margin: 0 auto;
  line-height: 1.4;
}

/* Responsive auth screen */
@media (max-width: 480px) {
  .auth-container {
    max-width: 100%;
    gap: 24px;
  }

  .auth-branding h1 {
    font-size: var(--font-size-card-header);
  }

  .auth-features {
    flex-direction: column;
    gap: 12px;
  }

  .feature-item {
    flex-direction: row;
    justify-content: center;
    gap: 12px;
  }
}

/* Enhanced Dashboard */
.dashboard-screen {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 8px;
}

.dashboard-header h1 {
  margin-bottom: 8px;
}

.dashboard-empty-state {
  margin-top: 40px;
}

.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 40px 20px;
  text-align: center;
}

.empty-state-icon {
  font-size: 64px;
  margin-bottom: 8px;
}

.spending-categories-card {
  margin-bottom: 24px;
}

.spending-categories-content {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 32px;
  align-items: flex-start;
}

.chart-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.legend-section {
  min-width: 0;
}

.chart-center-content {
  text-align: center;
}

.center-amount {
  display: block;
  line-height: 1.2;
  margin-bottom: 4px;
}

.center-label {
  display: block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive dashboard */
@media (max-width: 768px) {
  .dashboard-screen {
    gap: 20px;
  }

  .spending-categories-content {
    grid-template-columns: 1fr;
    gap: 24px;
    text-align: center;
  }

  .chart-section {
    justify-self: center;
  }

  .legend-section {
    justify-self: stretch;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    text-align: left;
  }

  .empty-state-content {
    padding: 32px 16px;
  }

  .empty-state-icon {
    font-size: 48px;
  }

  .spending-categories-content {
    gap: 20px;
  }
}

/* Enhanced Upload Screen */
.upload-screen {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.upload-header {
  text-align: center;
  margin-bottom: 8px;
}

.upload-header h1 {
  margin-bottom: 8px;
}

.upload-section {
  margin-bottom: 2rem;
}

.separator {
  text-align: center;
  margin: 2rem 0;
  position: relative;
}

.separator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e5e7eb;
}

.separator span {
  background: #f5f5f5;
  padding: 0 1rem;
  color: #6b7280;
  font-weight: 500;
}

.upload-area {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.upload-option {
  margin-bottom: 2rem;
}

.upload-option label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.upload-option input[type="file"] {
  width: 100%;
  padding: 0.5rem;
  border: 2px dashed #ddd;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.upload-option textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-family: inherit;
  resize: vertical;
}

.upload-option small {
  color: #666;
  font-size: 0.9rem;
}

.separator-text {
  text-align: center;
  color: #666;
  margin: 1rem 0;
  font-weight: 600;
}

/* Upload Tabs */
.upload-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
  overflow: hidden;
}

.tab-headers {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-header {
  flex: 1;
  padding: 1rem 2rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.tab-header:hover:not(:disabled) {
  background: #e9ecef;
  color: #495057;
}

.tab-header.active {
  background: white;
  color: #007bff;
  border-bottom-color: #007bff;
}

.tab-header:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.tab-content {
  padding: 2rem;
  min-height: 300px;
}

/* File Upload Tab */
.upload-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-zone {
  width: 100%;
  max-width: 500px;
  position: relative;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-input-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  background: #f8fafc;
}

.file-input-label:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.upload-text {
  text-align: center;
}

.upload-message {
  display: block;
  font-size: 1.1rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.upload-hint {
  display: block;
  font-size: 0.9rem;
  color: #6b7280;
}

.file-selected {
  color: #059669;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Text Input Tab */
.text-tab {
  display: flex;
  flex-direction: column;
}

.text-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s;
}

.text-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.text-counter {
  text-align: right;
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

/* Analysis Section */
.analysis-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.analysis-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.analysis-header h3 {
  margin: 0;
  color: #374151;
}

.help-button {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;
}

.help-button:hover {
  background: #e5e7eb;
  transform: scale(1.05);
}

/* Help Panel */
.help-panel {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

.help-panel.closing {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    max-height: 300px;
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    max-height: 300px;
    opacity: 1;
    transform: translateY(0);
  }
  to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
}

.help-item {
  margin-bottom: 1rem;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-mode {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.help-description {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Analysis buttons container */
.analysis-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.analyze-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
  min-width: 160px;
  max-width: 220px;
  font-weight: 500;
}

.analyze-button:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.analyze-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Specific styles for analysis modes */
.basic-analysis {
  background: #28a745;
}

.basic-analysis:hover:not(:disabled) {
  background: #1e7e34;
}

.advanced-analysis {
  background: #007bff;
}

.advanced-analysis:hover:not(:disabled) {
  background: #0056b3;
}

.analysis-status {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.analysis-status.processing {
  background: #e3f2fd;
  color: #1976d2;
}

.analysis-status.success {
  background: #e8f5e8;
  color: #2e7d32;
}

.analysis-status.error {
  background: #ffebee;
  color: #c62828;
}

/* Document Upload Styles */
.document-upload {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.upload-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.upload-zone:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: #f3f4f6;
}

.upload-message {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
}

.progress-bar {
  width: 100%;
  max-width: 300px;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease;
}

.upload-hint {
  margin: 0;
}

.upload-features {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.upload-features h4 {
  margin: 0 0 0.5rem 0;
}

.upload-features ul {
  margin: 0;
  padding-left: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 8px 12px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .user-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .app-main {
    padding: 1rem;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .transaction-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .transaction-amount {
    text-align: left;
  }

  .bottom-nav {
    padding: 0.5rem;
  }
}

/* Enhanced Upload Screen Styles */
.upload-tabs-card {
  margin-bottom: 24px;
}

.upload-tabs {
  display: flex;
  flex-direction: column;
}

.upload-tabs .tab-headers {
  display: flex;
  background-color: var(--border-color-lighter);
  border-radius: var(--border-radius-small);
  padding: 4px;
  gap: 2px;
  margin-bottom: 24px;
}

.upload-tabs .tab-header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: var(--border-radius-small);
  background: none;
  cursor: pointer;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-secondary);
  transition: all 0.2s ease;
}

.upload-tabs .tab-header:hover:not(:disabled) {
  background-color: var(--primary-green-light-tint);
  color: var(--primary-green);
}

.upload-tabs .tab-header.active {
  background-color: var(--primary-green);
  color: var(--text-color-on-primary);
  box-shadow: 0 2px 4px rgba(46, 139, 87, 0.2);
}

.upload-tabs .tab-header:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-tabs .tab-content {
  min-height: 300px;
}

/* Enhanced File Upload */
.upload-zone {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: 48px 24px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  background-color: var(--light-tint-grey);
}

.upload-zone:hover {
  border-color: var(--primary-green);
  background-color: var(--primary-green-extra-light-tint);
}

.upload-zone.drag-over {
  border-color: var(--primary-green);
  background-color: var(--primary-green-light-tint);
  transform: scale(1.02);
}

.upload-zone.has-file {
  border-color: var(--semantic-credit);
  background-color: var(--primary-green-extra-light-tint);
}

.upload-zone .file-input {
  display: none;
}

.upload-zone .file-input-label {
  cursor: pointer;
  display: block;
  width: 100%;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-content .upload-icon {
  color: var(--text-color-secondary);
  transition: color 0.2s ease;
}

.upload-content .upload-icon.success {
  color: var(--semantic-credit);
}

.upload-content .upload-text {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: center;
}

.upload-content .upload-message {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.upload-content .upload-hint {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
}

.upload-content .file-selected {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-semibold);
  color: var(--semantic-credit);
}

.upload-content .file-size {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
}

/* Enhanced Text Input */
.text-tab {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-tab .text-input {
  font-family: 'Courier New', monospace;
  font-size: var(--font-size-small);
  min-height: 300px;
}

.text-counter {
  text-align: right;
}

/* Enhanced Analysis Section */
.analysis-section {
  margin-bottom: 24px;
}

.analysis-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.help-panel {
  background-color: var(--light-tint-grey);
  border-radius: var(--border-radius-small);
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid var(--border-color-lighter);
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

.help-panel.closing {
  animation: slideUp 0.3s ease-out;
}

.help-item {
  margin-bottom: 20px;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.help-mode {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
}

.help-description {
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
  line-height: 1.5;
  margin: 0;
}

.analysis-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.analysis-button {
  min-height: 56px;
}

  .nav-button {
    padding: 0.5rem;
    font-size: 0.8rem;
  } 