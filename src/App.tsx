import { useState, useEffect } from 'react';
import { Routes, Route, useNavigate, Navigate, useLocation } from 'react-router-dom';
import { authService } from './services/auth';
import { apiService } from './services/api';
import { AuthScreen } from './components/AuthScreen';
import { Dashboard } from './components/Dashboard';
import { UploadScreen } from './components/UploadScreen';
import { TransactionsScreen } from './components/TransactionsScreen';
import { InsightsScreen } from './components/InsightsScreen';
import { SettingsScreen } from './components/SettingsScreen';
import { BottomNavigation, ScreenType } from './components/BottomNavigation';
import { SkipLink, useKeyboardNavigation } from './components/AccessibilityUtils';
import { User, Transaction, TransactionStats } from './types';
import './App.css';

function App() {
  const [user, setUser] = useState<User | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [_stats, _setStats] = useState<TransactionStats>({
    totalIncome: 0,
    totalExpenses: 0,
    netCashFlow: 0,
    transactionCount: 0,
    categoryBreakdown: {}
  });
  const [loading, setLoading] = useState(true);
  const [categoryFilter, setCategoryFilter] = useState<string | undefined>(undefined);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);

  // Performance and accessibility hooks
  useKeyboardNavigation();

  const navigate = useNavigate();
  const location = useLocation();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showProfileDropdown && !target.closest('.profile-dropdown-container')) {
        setShowProfileDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showProfileDropdown]);

  useEffect(() => {
    const unsubscribe = authService.onAuthStateChanged((user) => {
      setUser(user);
      setLoading(false);
      if (user && location.pathname === '/login') {
        navigate('/dashboard', { replace: true });
      }
    });

    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (user) {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const loadData = async () => {
    try {
      const [transactionsResponse, statsResponse] = await Promise.all([
        apiService.getTransactions(),
        apiService.getTransactionStats()
      ]);

      if (transactionsResponse.success) {
        setTransactions(transactionsResponse.data || []);
      }

      if (statsResponse.success) {
        _setStats(statsResponse.data || {
          totalIncome: 0,
          totalExpenses: 0,
          netCashFlow: 0,
          transactionCount: 0,
          categoryBreakdown: {}
        });
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const handleSignIn = () => {
    navigate('/dashboard');
  };

  const handleSignOut = async () => {
    try {
      await authService.signOut();
      setUser(null);
      setTransactions([]);
      _setStats({
        totalIncome: 0,
        totalExpenses: 0,
        netCashFlow: 0,
        transactionCount: 0,
        categoryBreakdown: {}
      });
      setShowProfileDropdown(false);
      navigate('/login');
    } catch (error) {
      console.error('Sign-out failed:', error);
    }
  };

  // Get screen subtitle based on current route
  const getScreenSubtitle = (pathname: string): string => {
    switch (pathname) {
      case '/dashboard':
        return 'Your Financial Snapshot';
      case '/upload':
        return 'Analyze a New Statement';
      case '/transactions':
        return 'View & Manage Transactions';
      case '/insights':
        return 'Your Financial Habits';
      case '/settings':
        return 'Account & Preferences';
      default:
        return 'Your Financial Snapshot';
    }
  };

  // Get user initial for profile avatar
  const getUserInitial = (user: User): string => {
    if (user.displayName) {
      return user.displayName.charAt(0).toUpperCase();
    }
    if (user.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return 'U';
  };

  // Handle profile dropdown toggle
  const toggleProfileDropdown = () => {
    setShowProfileDropdown(!showProfileDropdown);
  };

  // Handle navigation to settings
  const handleNavigateToSettings = () => {
    navigate('/settings');
    setShowProfileDropdown(false);
  };

  const handleAnalysisComplete = () => {
    loadData();
    navigate('/dashboard');
  };

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  if (!user) {
    // Only allow AuthScreen on /login, otherwise redirect
    if (location.pathname !== '/login') {
      return <Navigate to="/login" replace />;
    }
    return <AuthScreen onSignIn={handleSignIn} />;
  }

  return (
    <div className="app">
      {/* Skip links for accessibility */}
      <SkipLink href="#main-content">Skip to main content</SkipLink>
      <SkipLink href="#navigation">Skip to navigation</SkipLink>

      <header className="app-header" role="banner">
        <div className="header-title-container">
          <h1 id="header-title">FinScope</h1>
          <div id="header-subtitle">{getScreenSubtitle(location.pathname)}</div>
        </div>

        <div className="profile-dropdown-container">
          <div
            id="profile-avatar"
            className={`profile-avatar ${!user ? 'hidden' : ''}`}
            onClick={toggleProfileDropdown}
            role="button"
            tabIndex={0}
            aria-label="Open profile menu"
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleProfileDropdown();
              }
            }}
          >
            {user && getUserInitial(user)}
          </div>

          <ul
            id="profile-dropdown-menu"
            className={`profile-dropdown-menu ${!showProfileDropdown ? 'hidden' : ''}`}
          >
            <li>
              <button
                onClick={handleNavigateToSettings}
                className="dropdown-item"
              >
                Settings
              </button>
            </li>
            <li>
              <button
                onClick={handleSignOut}
                className="dropdown-item"
              >
                Sign Out
              </button>
            </li>
          </ul>
        </div>
      </header>

      <main id="main-content" className="main-content" tabIndex={-1}>
        <Routes>
          <Route path="/dashboard" element={
            <Dashboard
              transactions={transactions}
              onNavigateToTransactions={(categoryFilter) => {
                setCategoryFilter(categoryFilter);
                navigate('/transactions');
              }}
              onNavigateToUpload={() => navigate('/upload')}
            />
          } />
          <Route path="/upload" element={
            <UploadScreen onAnalysisComplete={handleAnalysisComplete} />
          } />
          <Route path="/transactions" element={
            <TransactionsScreen
              transactions={transactions}
              onNavigateToUpload={() => navigate('/upload')}
              initialCategoryFilter={categoryFilter}
              onClearCategoryFilter={() => setCategoryFilter(undefined)}
            />
          } />
          <Route path="/insights" element={
            <InsightsScreen
              transactions={transactions}
              onNavigateToUpload={() => navigate('/upload')}
            />
          } />
          <Route path="/settings" element={
            <SettingsScreen user={user} onSignOut={handleSignOut} />
          } />
          <Route path="/login" element={<Navigate to="/dashboard" replace />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </main>

      <BottomNavigation
        currentScreen={location.pathname.replace('/', '') as ScreenType}
        onScreenChange={(screen) => {
          navigate(`/${screen}`);
        }}
      />
    </div>
  );
}

export default App; 