import { supabase } from '../supabaseClient';

// Database table name
const TRANSACTIONS_TABLE = 'transactions';

// Transaction interface matching your existing one
export interface Transaction {
    id: string;
    user_id: string;
    fingerprint?: string;
    date: string;
    time: string | null;
    description: string;
    amount: number;
    type: 'credit' | 'debit';
    category?: string;
    vendor?: string;
    bank_provided_id: string | null;
    source_bank: string;
    created_at?: string;
    updated_at?: string;
}

// Database service functions
export class TransactionService {
    // Get all transactions for a user
    static async getTransactions(userId: string): Promise<Transaction[]> {
        try {
            const { data, error } = await supabase
                .from(TRANSACTIONS_TABLE)
                .select('*')
                .eq('user_id', userId)
                .order('date', { ascending: false });

            if (error) {
                console.error('Error fetching transactions:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('Failed to fetch transactions:', error);
            throw error;
        }
    }

    // Add a single transaction
    static async addTransaction(transaction: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>): Promise<Transaction> {
        try {
            const { data, error } = await supabase
                .from(TRANSACTIONS_TABLE)
                .insert([transaction])
                .select()
                .single();

            if (error) {
                console.error('Error adding transaction:', error);
                throw error;
            }

            return data;
        } catch (error) {
            console.error('Failed to add transaction:', error);
            throw error;
        }
    }

    // Add multiple transactions in a batch
    static async addTransactions(transactions: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>[]): Promise<Transaction[]> {
        try {
            const { data, error } = await supabase
                .from(TRANSACTIONS_TABLE)
                .insert(transactions)
                .select();

            if (error) {
                console.error('Error adding transactions:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('Failed to add transactions:', error);
            throw error;
        }
    }

    // Update a transaction
    static async updateTransaction(id: string, updates: Partial<Transaction>): Promise<Transaction> {
        try {
            const { data, error } = await supabase
                .from(TRANSACTIONS_TABLE)
                .update(updates)
                .eq('id', id)
                .select()
                .single();

            if (error) {
                console.error('Error updating transaction:', error);
                throw error;
            }

            return data;
        } catch (error) {
            console.error('Failed to update transaction:', error);
            throw error;
        }
    }

    // Delete a transaction
    static async deleteTransaction(id: string): Promise<void> {
        try {
            const { error } = await supabase
                .from(TRANSACTIONS_TABLE)
                .delete()
                .eq('id', id);

            if (error) {
                console.error('Error deleting transaction:', error);
                throw error;
            }
        } catch (error) {
            console.error('Failed to delete transaction:', error);
            throw error;
        }
    }

    // Get transactions by date range
    static async getTransactionsByDateRange(userId: string, startDate: string, endDate: string): Promise<Transaction[]> {
        try {
            const { data, error } = await supabase
                .from(TRANSACTIONS_TABLE)
                .select('*')
                .eq('user_id', userId)
                .gte('date', startDate)
                .lte('date', endDate)
                .order('date', { ascending: false });

            if (error) {
                console.error('Error fetching transactions by date range:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('Failed to fetch transactions by date range:', error);
            throw error;
        }
    }

    // Get transactions by category
    static async getTransactionsByCategory(userId: string, category: string): Promise<Transaction[]> {
        try {
            const { data, error } = await supabase
                .from(TRANSACTIONS_TABLE)
                .select('*')
                .eq('user_id', userId)
                .eq('category', category)
                .order('date', { ascending: false });

            if (error) {
                console.error('Error fetching transactions by category:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('Failed to fetch transactions by category:', error);
            throw error;
        }
    }

    // Search transactions by description
    static async searchTransactions(userId: string, searchTerm: string): Promise<Transaction[]> {
        try {
            const { data, error } = await supabase
                .from(TRANSACTIONS_TABLE)
                .select('*')
                .eq('user_id', userId)
                .ilike('description', `%${searchTerm}%`)
                .order('date', { ascending: false });

            if (error) {
                console.error('Error searching transactions:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('Failed to search transactions:', error);
            throw error;
        }
    }

    // Get transaction statistics
    static async getTransactionStats(userId: string, startDate?: string, endDate?: string): Promise<{
        totalIncome: number;
        totalExpenses: number;
        netCashFlow: number;
        transactionCount: number;
        categoryBreakdown: Record<string, number>;
    }> {
        try {
            let query = supabase
                .from(TRANSACTIONS_TABLE)
                .select('*')
                .eq('user_id', userId);

            if (startDate && endDate) {
                query = query.gte('date', startDate).lte('date', endDate);
            }

            const { data, error } = await query;

            if (error) {
                console.error('Error fetching transaction stats:', error);
                throw error;
            }

            const transactions = data || [];
            const totalIncome = transactions
                .filter(t => t.type === 'credit')
                .reduce((sum, t) => sum + t.amount, 0);
            
            const totalExpenses = transactions
                .filter(t => t.type === 'debit')
                .reduce((sum, t) => sum + t.amount, 0);

            const categoryBreakdown: Record<string, number> = {};
            transactions.forEach(t => {
                if (t.category) {
                    categoryBreakdown[t.category] = (categoryBreakdown[t.category] || 0) + t.amount;
                }
            });

            return {
                totalIncome,
                totalExpenses,
                netCashFlow: totalIncome - totalExpenses,
                transactionCount: transactions.length,
                categoryBreakdown
            };
        } catch (error) {
            console.error('Failed to get transaction stats:', error);
            throw error;
        }
    }
} 