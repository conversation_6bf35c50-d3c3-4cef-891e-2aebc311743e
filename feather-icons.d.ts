declare module 'feather-icons' {
  interface FeatherIcon {
    toSvg(options?: {
      class?: string;
      width?: number | string;
      height?: number | string;
      'stroke-width'?: number | string;
      stroke?: string;
      fill?: string;
    }): string;
  }

  interface FeatherIcons {
    [iconName: string]: FeatherIcon;
  }

  interface FeatherModule {
    icons: FeatherIcons;
  }

  const feather: FeatherModule;
  export = feather;
}
