{"name": "finscope", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "rm -rf dist && find src -name '*.js' -delete && find src -name '*.d.ts' -delete && find src -name '*.map' -delete", "prebuild": "npm run clean", "start": "node dist/index.js", "dev:backend": "cd backend/services/document-processor && npm run dev", "dev:frontend": "vite", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@google/genai": "^1.9.0", "@playwright/test": "^1.54.1", "@supabase/supabase-js": "^2.50.5", "@types/pg": "^8.15.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "axios": "^1.10.0", "bull": "^4.16.5", "cors": "^2.8.5", "csv-parse": "^6.0.0", "dotenv": "^17.2.0", "express": "^5.1.0", "feather-icons": "^4.29.2", "google-auth-library": "^10.1.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "multer": "^2.0.1", "pg": "^8.16.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "uuid": "^11.1.0", "vitest": "^3.2.4", "xlsx": "^0.18.5"}, "workspaces": ["backend/shared", "backend/services/*"], "devDependencies": {"@types/morgan": "^1.9.10", "@types/node": "^22.14.0", "concurrently": "^9.2.0", "ts-node": "^10.9.2", "typescript": "~5.7.2", "vite": "^6.2.0"}}