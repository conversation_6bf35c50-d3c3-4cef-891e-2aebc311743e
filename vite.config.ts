import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      plugins: [react()],
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      envPrefix: 'VITE_',
      server: {
        port: 5173, // Default port
        strictPort: false, // Allow Vite to pick another port if 5173 is in use
        proxy: {
          '/api/v1': {
            target: 'http://localhost:3001',
            changeOrigin: true,
          },
          '/health': {
            target: 'http://localhost:3001',
            changeOrigin: true,
          }
        }
      },
      // NOTE: Change proxy target back to 3001 or your Cloud Run URL for production deployment.
    };
});
