export interface Transaction {
  id?: string;
  user_id: string;
  fingerprint?: string;
  date: string;
  time: string | null;
  description: string;
  amount: number;
  type: 'credit' | 'debit';
  category?: string;
  vendor?: string;
  bank_provided_id: string | null;
  source_bank: string;
  
  // Enhanced optional fields
  sender_name?: string;
  receiver_name?: string;
  sender_account_number?: string;
  receiver_account_number?: string;
  account_type?: string;
  customer_id?: string;
  transaction_fee?: number;
  currency_type?: string;
  exchange_rate?: number;
  transaction_status?: string;
  payment_method?: string;
  narration?: string;
  invoice_number?: string;
  merchant_name?: string;
  authorization_code?: string;
  destination_bank?: string;
  bank_branch?: string;
  payment_gateway?: string;
  terminal_id?: string;
  channel?: string;
  session_id?: string;
  device_id?: string;
  ip_address?: string;
  geolocation?: string;
  balance_before?: number;
  balance_after?: number;
  
  created_at?: string;
  updated_at?: string;
}

export interface AnalysisRequest {
  text?: string;
  file?: Express.Multer.File;
}

export interface AnalysisResponse {
  success: boolean;
  transactions: Transaction[];
  detectedBank: string;
  message?: string;
  error?: string;
}

import { Request } from 'express';

export interface AuthRequest extends Request {
    user?: {
        uid: string;
        email: string;
    };
    file?: Express.Multer.File;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface TransactionStats {
  totalIncome: number;
  totalExpenses: number;
  netCashFlow: number;
  transactionCount: number;
  categoryBreakdown: Record<string, number>;
}

export interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
}

export type ScreenType = 'dashboard' | 'upload' | 'transactions' | 'insights' | 'settings';

// Enhanced types for document processing
export interface ExtractedData {
  transactions: Transaction[];
  accountInfo: AccountInfo;
  summary: TransactionSummary;
  metadata: DocumentMetadata;
}

export interface AccountInfo {
  accountNumber?: string;
  accountName?: string;
  accountType?: string;
  bankName?: string;
  branch?: string;
  balance?: number;
  currency?: string;
}

export interface TransactionSummary {
  totalCredits: number;
  totalDebits: number;
  netAmount: number;
  transactionCount: number;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface DocumentMetadata {
  documentType: 'receipt' | 'bank_statement' | 'transaction_slip' | 'unknown';
  source: string;
  processingTimestamp: string;
  confidence: number;
}

export interface ProcessingResult {
  success: boolean;
  metadata?: {
    documentType: 'receipt' | 'bank_statement' | 'transaction_slip' | 'unknown';
    source: string;
    timestamp: Date;
    fileSize: number;
    mimeType: string;
  };
  extractedData?: ExtractedData;
  confidenceScores?: ConfidenceScore;
  processingTime: number;
  rawText?: string;
  error?: string;
  batchId?: string;
  processingId?: string;
  supabaseResult?: SupabaseInsertResult;
}

export interface ConfidenceScore {
  overall: number;
  textExtraction: number;
  dataParsing: number;
  validation: number;
}

// New types for enhanced features
export interface BatchMetadata {
  batchId: string;
  source: 'pdf' | 'email' | 'image';
  fileName: string;
  uploadedBy: string;
  uploadedAt: Date;
  totalDocuments: number;
  status: 'processing' | 'completed' | 'failed' | 'partial';
  processedCount: number;
  failedCount: number;
}

export interface ProcessingLog {
  processingId: string;
  batchId?: string;
  userId: string;
  fileName: string;
  status: 'started' | 'processing' | 'completed' | 'failed';
  stage: 'document_analysis' | 'text_extraction' | 'data_parsing' | 'validation' | 'supabase_insert';
  timestamp: string; // changed from Date to string
  message: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface AuditTrail {
  id: string;
  userId: string;
  action: 'document_upload' | 'processing_start' | 'processing_complete' | 'processing_error' | 'transaction_insert';
  resourceType: 'document' | 'transaction' | 'batch';
  resourceId: string;
  timestamp: Date;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

export interface ProcessingOptions {
  enableAdvancedAnalysis?: boolean;
  enableDeduplication?: boolean;
  enableRetry?: boolean;
  maxRetries?: number;
  batchSize?: number;
  enableLogging?: boolean;
  enableAuditTrail?: boolean;
  batchId?: string;
}

export interface GeminiResponse {
  success: boolean;
  data?: any;
  error?: string;
  rawResponse?: string;
  tokenUsage?: {
    input: number;
    output: number;
    total: number;
  };
}

export interface SupabaseInsertResult {
  success: boolean;
  insertedCount: number;
  duplicateCount: number;
  errorCount: number;
  errors?: string[];
  transactionIds?: string[];
} 