# FinScope Microservices Deployment Guide

This guide covers deploying both the Node.js Document Processor and Python PDF Processor services to Google Cloud Run.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │ Document Processor│    │ PDF Processor   │
│   (React)       │───▶│   (Node.js)      │───▶│   (Python)      │
│                 │    │   Port: 3002     │    │   Port: 8080    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Service Responsibilities

### Document Processor (Node.js)
- **Port**: 3002
- **Handles**: Images (JPG, PNG), AI processing, Supabase integration
- **Features**: Gemini OCR, transaction parsing, Firebase logging

### PDF Processor (Python)
- **Port**: 8080
- **Handles**: PDF files, text extraction, image conversion
- **Features**: pdfplumber, PyMuPDF, image optimization

## Prerequisites

1. **Google Cloud Project** with billing enabled
2. **Google Cloud CLI** installed and configured
3. **Docker** installed locally
4. **Firebase project** configured
5. **Supabase project** configured

## Environment Variables

### Document Processor (.env)
```bash
# Server Configuration
PORT=3002
NODE_ENV=production

# Frontend URL for CORS
FRONTEND_URL=https://your-frontend-domain.com

# Google Gemini AI
GEMINI_API_KEY=your_gemini_api_key

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Firebase Configuration
FIREBASE_PROJECT_ID=finscope-kaycee
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour Key Here\n-----END PRIVATE KEY-----\n"
FIREBASE_STORAGE_BUCKET=finscope-kaycee.appspot.com

# PDF Service Configuration
PDF_SERVICE_URL=https://pdf-processor-xxxxx-uc.a.run.app
```

### PDF Processor (.env)
```bash
# Server Configuration
PORT=8080
NODE_ENV=production

# Firebase Configuration
FIREBASE_PROJECT_ID=finscope-kaycee
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour Key Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_CLIENT_X509_CERT_URL=your_cert_url
FIREBASE_STORAGE_BUCKET=finscope-kaycee.appspot.com

# Processing Configuration
MAX_FILE_SIZE=10485760
MAX_BATCH_SIZE=10
PDF_DPI=300
TEXT_EXTRACTION_THRESHOLD=100
```

## Deployment Steps

### 1. Build and Deploy PDF Processor (Python)

```bash
# Navigate to PDF processor directory
cd backend/services/pdf-processor

# Build Docker image
docker build -t gcr.io/finscope-kaycee/pdf-processor .

# Push to Google Container Registry
docker push gcr.io/finscope-kaycee/pdf-processor

# Deploy to Cloud Run
gcloud run deploy pdf-processor \
  --image gcr.io/finscope-kaycee/pdf-processor \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --timeout 900 \
  --max-instances 10 \
  --set-env-vars "FIREBASE_PROJECT_ID=finscope-kaycee" \
  --set-env-vars "FIREBASE_STORAGE_BUCKET=finscope-kaycee.appspot.com"
```

### 2. Build and Deploy Document Processor (Node.js)

```bash
# Navigate to document processor directory
cd backend/services/document-processor

# Build TypeScript
npm run build

# Build Docker image
docker build -t gcr.io/finscope-kaycee/document-processor .

# Push to Google Container Registry
docker push gcr.io/finscope-kaycee/document-processor

# Deploy to Cloud Run
gcloud run deploy document-processor \
  --image gcr.io/finscope-kaycee/document-processor \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 1Gi \
  --cpu 1 \
  --timeout 300 \
  --max-instances 20 \
  --set-env-vars "PDF_SERVICE_URL=https://pdf-processor-xxxxx-uc.a.run.app" \
  --set-env-vars "GEMINI_API_KEY=your_gemini_api_key" \
  --set-env-vars "SUPABASE_URL=your_supabase_url" \
  --set-env-vars "SUPABASE_SERVICE_ROLE_KEY=your_supabase_key"
```

### 3. Update Frontend Configuration

Update your frontend to point to the new document processor service:

```typescript
// In your frontend API configuration
const DOCUMENT_PROCESSOR_URL = 'https://document-processor-xxxxx-uc.a.run.app';
const PDF_PROCESSOR_URL = 'https://pdf-processor-xxxxx-uc.a.run.app';
```

## Testing the Deployment

### 1. Test PDF Processor Health
```bash
curl https://pdf-processor-xxxxx-uc.a.run.app/health
```

### 2. Test Document Processor Health
```bash
curl https://document-processor-xxxxx-uc.a.run.app/health
```

### 3. Test PDF Processing
```bash
curl -X POST https://pdf-processor-xxxxx-uc.a.run.app/api/v1/process \
  -F "file=@test.pdf" \
  -F "userId=test-user"
```

### 4. Test Document Processing
```bash
curl -X POST https://document-processor-xxxxx-uc.a.run.app/api/v1/process \
  -F "document=@test-image.jpg" \
  -F "userId=test-user"
```

## Monitoring and Logging

### 1. Cloud Run Logs
```bash
# View PDF processor logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=pdf-processor" --limit=50

# View document processor logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=document-processor" --limit=50
```

### 2. Firebase Logs
- Processing logs: `processing_logs` collection
- PDF processing logs: `pdf_processing_logs` collection
- Error logs: `error_logs` collection
- Performance logs: `performance_logs` collection

### 3. Supabase Monitoring
- Check transaction insertion logs
- Monitor database performance
- Review error logs

## Scaling Configuration

### PDF Processor
- **Memory**: 2Gi (for image processing)
- **CPU**: 2 (for PDF conversion)
- **Max Instances**: 10 (PDF processing is resource-intensive)
- **Timeout**: 900s (15 minutes for large PDFs)

### Document Processor
- **Memory**: 1Gi (for AI processing)
- **CPU**: 1 (sufficient for API calls)
- **Max Instances**: 20 (can handle more concurrent requests)
- **Timeout**: 300s (5 minutes for AI processing)

## Security Considerations

1. **Authentication**: Both services are currently unauthenticated for simplicity. Add Firebase Auth middleware for production.
2. **CORS**: Configure CORS to only allow your frontend domain.
3. **Rate Limiting**: Consider adding rate limiting for production use.
4. **API Keys**: Store sensitive environment variables in Google Secret Manager.

## Troubleshooting

### Common Issues

1. **PDF Service Unavailable**
   - Check if PDF processor is running
   - Verify environment variables
   - Check Cloud Run logs

2. **Memory Issues**
   - Increase memory allocation
   - Optimize image processing
   - Reduce batch sizes

3. **Timeout Issues**
   - Increase timeout values
   - Optimize processing logic
   - Use async processing for large files

4. **CORS Errors**
   - Verify FRONTEND_URL configuration
   - Check CORS headers in responses

### Debug Commands

```bash
# Check service status
gcloud run services list

# View service details
gcloud run services describe pdf-processor
gcloud run services describe document-processor

# Check environment variables
gcloud run services describe pdf-processor --format="value(spec.template.spec.containers[0].env[].name,spec.template.spec.containers[0].env[].value)"
```

## Cost Optimization

1. **Use appropriate instance sizes**
2. **Set reasonable max instances**
3. **Monitor usage patterns**
4. **Use Cloud Run's automatic scaling**
5. **Consider cold start optimization**

## Next Steps

1. **Add authentication middleware**
2. **Implement rate limiting**
3. **Add comprehensive monitoring**
4. **Set up alerts for errors**
5. **Optimize for cost and performance** 