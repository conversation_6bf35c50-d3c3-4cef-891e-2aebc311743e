import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import multer from 'multer';
import 'dotenv/config';
import { DocumentProcessor } from './document-processor';
import { SupabaseDocumentService } from './utils/supabase-service';
import JobQueueService from './utils/job-queue';
import { ProcessingOptions } from './types';

const app = express();
const PORT = process.env.PORT || 8080;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (_req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and PDF are allowed.'));
    }
  }
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({
    success: true,
    message: 'Document Processor Service is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API Routes
const apiRouter = express.Router();

// Document processing endpoint (async)
apiRouter.post('/process', upload.single('document'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No document file provided'
      });
    }

    // Extract user ID from request (you'll need to implement auth middleware)
    const userId = req.body.userId || req.headers['x-user-id'] as string;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User ID is required'
      });
    }

    const fileName = req.file.originalname;
    const options: ProcessingOptions = {
      enableAdvancedAnalysis: req.body.enableAdvancedAnalysis === 'true',
      enableDeduplication: req.body.enableDeduplication !== 'false',
      enableRetry: req.body.enableRetry !== 'false',
      maxRetries: parseInt(req.body.maxRetries) || 3,
      batchSize: parseInt(req.body.batchSize) || 100,
      enableLogging: req.body.enableLogging !== 'false',
      enableAuditTrail: req.body.enableAuditTrail !== 'false',
      batchId: req.body.batchId
    };

    console.log(`📄 Queuing document: ${fileName} for user: ${userId}`);

    // Add job to queue instead of processing immediately
    const jobId = await JobQueueService.addDocumentJob(
      req.file.buffer,
      req.file.mimetype,
      userId,
      fileName,
      options
    );

    return res.json({
      success: true,
      data: {
        jobId,
        status: 'queued',
        message: 'Document queued for processing'
      },
      message: 'Document uploaded successfully and queued for processing'
    });

  } catch (error) {
    console.error('Document queuing error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error during document queuing'
    });
  }
});

// Synchronous processing endpoint (for backward compatibility)
apiRouter.post('/process-sync', upload.single('document'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No document file provided'
      });
    }

    // Extract user ID from request (you'll need to implement auth middleware)
    const userId = req.body.userId || req.headers['x-user-id'] as string;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User ID is required'
      });
    }

    const fileName = req.file.originalname;
    const options: ProcessingOptions = {
      enableAdvancedAnalysis: req.body.enableAdvancedAnalysis === 'true',
      enableDeduplication: req.body.enableDeduplication !== 'false',
      enableRetry: req.body.enableRetry !== 'false',
      maxRetries: parseInt(req.body.maxRetries) || 3,
      batchSize: parseInt(req.body.batchSize) || 100,
      enableLogging: req.body.enableLogging !== 'false',
      enableAuditTrail: req.body.enableAuditTrail !== 'false',
      batchId: req.body.batchId
    };

    console.log(`📄 Processing document synchronously: ${fileName} for user: ${userId}`);

    const result = await DocumentProcessor.processDocument(
      req.file.buffer,
      req.file.mimetype,
      userId,
      fileName,
      options
    );

    return res.json({
      success: result.success,
      data: result,
      message: result.success ? 'Document processed successfully' : 'Document processing failed'
    });

  } catch (error) {
    console.error('Document processing error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error during document processing'
    });
  }
});

// Batch processing endpoint
apiRouter.post('/batch', upload.array('documents', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No documents provided'
      });
    }

    const userId = req.body.userId || req.headers['x-user-id'] as string;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User ID is required'
      });
    }

    const batchId = req.body.batchId || `batch_${Date.now()}`;
    const options: ProcessingOptions = {
      enableAdvancedAnalysis: req.body.enableAdvancedAnalysis === 'true',
      enableDeduplication: req.body.enableDeduplication !== 'false',
      enableRetry: req.body.enableRetry !== 'false',
      maxRetries: parseInt(req.body.maxRetries) || 3,
      batchSize: parseInt(req.body.batchSize) || 100,
      enableLogging: req.body.enableLogging !== 'false',
      enableAuditTrail: req.body.enableAuditTrail !== 'false',
      batchId
    };

    console.log(`📦 Processing batch: ${batchId} with ${req.files.length} documents`);

    // Create batch metadata
    const batchMetadata = {
      batchId,
      source: 'image' as const,
      fileName: `batch_${batchId}`,
      uploadedBy: userId,
      uploadedAt: new Date(),
      totalDocuments: (req.files as Express.Multer.File[]).length,
      status: 'processing' as const,
      processedCount: 0,
      failedCount: 0
    };

    await SupabaseDocumentService.createBatchMetadata(batchMetadata);

    // Process documents in parallel
    const processingPromises = (req.files as Express.Multer.File[]).map(async (file) => {
      try {
        const result = await DocumentProcessor.processDocument(
          file.buffer,
          file.mimetype,
          userId,
          file.originalname,
          { ...options, batchId }
        );

        return {
          fileName: file.originalname,
          success: result.success,
          result
        };
      } catch (error) {
        return {
          fileName: file.originalname,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    const results = await Promise.all(processingPromises);

    // Update batch status
    const successCount = results.filter(r => r.success).length;
    const failedCount = results.length - successCount;
    const finalStatus = failedCount === 0 ? 'completed' : failedCount === results.length ? 'failed' : 'partial';

    await SupabaseDocumentService.updateBatchStatus(batchId, finalStatus, {
      processedCount: successCount,
      failedCount
    });

    return res.json({
      success: true,
      data: {
        batchId,
        totalDocuments: results.length,
        processedCount: successCount,
        failedCount,
        status: finalStatus,
        results
      },
      message: `Batch processing completed: ${successCount} successful, ${failedCount} failed`
    });

  } catch (error) {
    console.error('Batch processing error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error during batch processing'
    });
  }
});

// Get processing history
apiRouter.get('/history/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    const limit = parseInt(req.query.limit as string) || 50;

    const history = await SupabaseDocumentService.getProcessingHistory(userId, limit);

    return res.json({
      success: true,
      data: history,
      message: 'Processing history retrieved successfully'
    });

  } catch (error) {
    console.error('Error getting processing history:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to retrieve processing history'
    });
  }
});

// Get batch status
apiRouter.get('/batch/:batchId', async (req, res) => {
  try {
    const batchId = req.params.batchId;

    const batchStatus = await SupabaseDocumentService.getBatchStatus(batchId);

    if (!batchStatus) {
      return res.status(404).json({
        success: false,
        error: 'Batch not found'
      });
    }

    return res.json({
      success: true,
      data: batchStatus,
      message: 'Batch status retrieved successfully'
    });

  } catch (error) {
    console.error('Error getting batch status:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to retrieve batch status'
    });
  }
});

// Job status endpoints
apiRouter.get('/job/:jobId', async (req, res) => {
  try {
    const jobId = req.params.jobId;

    const jobStatus = await JobQueueService.getJobStatus(jobId);

    if (!jobStatus) {
      return res.status(404).json({
        success: false,
        error: 'Job not found'
      });
    }

    return res.json({
      success: true,
      data: jobStatus,
      message: 'Job status retrieved successfully'
    });

  } catch (error) {
    console.error('Error getting job status:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to retrieve job status'
    });
  }
});

// Get user's job history
apiRouter.get('/jobs/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    const limit = parseInt(req.query.limit as string) || 20;

    const jobs = await JobQueueService.getUserJobs(userId, limit);

    return res.json({
      success: true,
      data: jobs,
      message: 'Job history retrieved successfully'
    });

  } catch (error) {
    console.error('Error getting job history:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to retrieve job history'
    });
  }
});

// Cancel a job
apiRouter.delete('/job/:jobId', async (req, res) => {
  try {
    const jobId = req.params.jobId;

    const cancelled = await JobQueueService.cancelJob(jobId);

    if (!cancelled) {
      return res.status(404).json({
        success: false,
        error: 'Job not found or cannot be cancelled'
      });
    }

    return res.json({
      success: true,
      message: 'Job cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling job:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to cancel job'
    });
  }
});

// Get queue statistics
apiRouter.get('/queue/stats', async (_req, res) => {
  try {
    const stats = await JobQueueService.getQueueStats();

    return res.json({
      success: true,
      data: stats,
      message: 'Queue statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Error getting queue stats:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to retrieve queue statistics'
    });
  }
});

// Mount API routes
app.use('/api/v1', apiRouter);

// Error handling middleware
app.use((error: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error('Unhandled error:', error);
  
  if (error instanceof multer.MulterError) {
    return res.status(400).json({
      success: false,
      error: 'File upload error: ' + error.message
    });
  }

  return res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use('*', (_req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Document Processor Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API base URL: http://localhost:${PORT}/api/v1`);
});

export default app; 