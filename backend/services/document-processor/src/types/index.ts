// Core transaction types
export interface Transaction {
  id: string;
  user_id: string;
  fingerprint?: string;
  date: string;
  time: string | null;
  description: string;
  amount: number;
  type: 'credit' | 'debit';
  category?: string;
  vendor?: string;
  notes?: string;
  bank_provided_id: string | null;
  source_bank: string;
  
  // Enhanced optional fields
  sender_name?: string;
  receiver_name?: string;
  sender_account_number?: string;
  receiver_account_number?: string;
  account_type?: string;
  customer_id?: string;
  transaction_fee?: number;
  currency_type?: string;
  exchange_rate?: number;
  transaction_status?: string;
  payment_method?: string;
  narration?: string;
  invoice_number?: string;
  merchant_name?: string;
  authorization_code?: string;
  destination_bank?: string;
  bank_branch?: string;
  payment_gateway?: string;
  terminal_id?: string;
  channel?: string;
  session_id?: string;
  device_id?: string;
  ip_address?: string;
  geolocation?: string;
  balance_before?: number;
  balance_after?: number;
  
  created_at?: string;
  updated_at?: string;
}

export interface AnalysisResponse {
  success: boolean;
  transactions: Transaction[];
  detectedBank: string;
  message?: string;
  error?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface TransactionStats {
  totalIncome: number;
  totalExpenses: number;
  netCashFlow: number;
  transactionCount: number;
  categoryBreakdown: Record<string, number>;
}

export interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
}

// Document processing types
export interface ProcessingOptions {
  enableAdvancedAnalysis?: boolean;
  enableDeduplication?: boolean;
  batchSize?: number;
  timeout?: number;
  batchId?: string;
  enableRetry?: boolean;
  maxRetries?: number;
  enableLogging?: boolean;
  enableAuditTrail?: boolean;
}

export interface ProcessingResult {
  success: boolean;
  extractedData?: ExtractedData;
  error?: string;
  confidence?: ConfidenceScore;
  confidenceScores?: ConfidenceScore;
  processingTime?: number;
  metadata?: any;
  batchId?: string;
  processingId?: string;
  rawText?: string;
  supabaseResult?: any;
}

export interface ExtractedData {
  transactions: Transaction[];
  confidence?: number;
  documentType?: string;
  source?: string;
  accountInfo?: {
    bankName?: string;
    accountNumber?: string;
    accountType?: string;
  };
  summary?: any;
  metadata?: any;
}

export interface ConfidenceScore {
  overall: number;
  fields: Record<string, number>;
  textExtraction?: number;
  dataParsing?: number;
  validation?: number;
}

export interface ProcessingLog {
  id: string;
  processingId?: string;
  batchId?: string;
  userId?: string;
  fileName?: string;
  status?: string;
  stage?: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error';
  message: string;
  data?: any;
  metadata?: any;
  error?: any;
}

export interface SupabaseInsertResult {
  id: string;
  created_at: string;
  insertedCount?: number;
  duplicateCount?: number;
  errorCount?: number;
  success?: boolean;
  errors?: any[];
  transactionIds?: string[];
}

export interface ExtractionContext {
  userId: string;
  fileName: string;
  processingId: string;
  options: ProcessingOptions;
  documentType?: string;
  source?: string;
}

export interface ExtractionResult {
  success: boolean;
  data?: ExtractedData;
  error?: string;
  confidence?: ConfidenceScore;
}

export interface GeminiResponse {
  success: boolean;
  data?: any;
  error?: string;
  rawResponse?: string;
  tokenUsage?: {
    input?: number;
    output?: number;
    total?: number;
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
}

export interface BatchMetadata {
  batchId: string;
  totalFiles?: number;
  processedFiles?: number;
  failedFiles?: number;
  startTime?: string;
  endTime?: string;
  status?: string;
  source?: string;
  fileName?: string;
  uploadedBy?: string;
  uploadedAt?: Date;
  totalDocuments?: number;
  processedCount?: number;
  failedCount?: number;
}

export interface AuditTrail {
  id: string;
  userId: string;
  action: string;
  timestamp: string;
  details: any;
}

// Auth types
export interface AuthRequest {
  user?: {
    uid: string;
    email: string;
    displayName?: string;
  };
  body: any;
  params: any;
  query: any;
  file?: any;
  files?: any[];
} 