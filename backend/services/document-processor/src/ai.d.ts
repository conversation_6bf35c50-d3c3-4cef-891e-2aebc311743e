import { Transaction } from './types';
export declare class AIService {
    private static ai;
    private static parseGeminiJsonResponse;
    private static makeResilientGeminiRequest;
    private static makeResilientGeminiMultimodalRequest;
    static analyzeText(text: string, analysisMode?: 'basic' | 'advanced'): Promise<{
        transactions: Transaction[];
        detectedBank: string;
    }>;
    static analyzeImage(imageBuffer: Buffer, analysisMode?: 'basic' | 'advanced'): Promise<{
        transactions: Transaction[];
        detectedBank: string;
    }>;
}
//# sourceMappingURL=ai.d.ts.map