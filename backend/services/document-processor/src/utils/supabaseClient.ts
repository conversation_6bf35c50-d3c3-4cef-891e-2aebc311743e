import { createClient, SupabaseClient } from '@supabase/supabase-js';

let _supabaseClient: SupabaseClient | null = null;

export function getSupabaseClient(): SupabaseClient {
  if (!_supabaseClient) {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabasePublishableKey = process.env.SUPABASE_PUBLISHABLE_KEY;
    const supabaseSecretKey = process.env.SUPABASE_SECRET_KEY;

    if (!supabaseUrl || !supabaseSecretKey) {
      throw new Error('SUPABASE_URL and SUPABASE_SECRET_KEY environment variables are required');
    }

    // Use service key for backend operations to bypass RLS
    _supabaseClient = createClient(supabaseUrl, supabaseSecretKey);
  }

  return _supabaseClient;
}

// For backward compatibility
export const supabaseClient = new Proxy({} as SupabaseClient, {
  get(_unused, prop) {
    return getSupabaseClient()[prop as keyof SupabaseClient];
  }
});