import axios from 'axios';
import { GoogleAuth } from 'google-auth-library';


export interface PDFProcessingResult {
  success: boolean;
  extraction_method: 'pdfplumber' | 'image_conversion';
  text?: string;
  images?: Array<{
    page_number: number;
    image_data: string; // base64 encoded
    mime_type: string;
    width: number;
    height: number;
    size_bytes: number;
  }>;
  has_text: boolean;
  filename: string;
  user_id: string;
  processed_at: string;
}

export interface PDFBatchResult {
  success: boolean;
  data: {
    batch_id: string;
    total_files: number;
    success_count: number;
    error_count: number;
    results: Array<{
      filename: string;
      success: boolean;
      extraction_method?: 'pdfplumber' | 'image_conversion';
      text?: string;
      images?: any[];
      has_text?: boolean;
      error?: string;
    }>;
  };
  message: string;
}

// Helper to get a Google Cloud Run ID token
async function getCloudRunIdToken(audience: string): Promise<string> {
  const auth = new GoogleAuth();
  const client = await auth.getIdTokenClient(audience);
  // @ts-ignore
  const { Authorization } = await client.getRequestHeaders();
  // The header is in the form 'Bearer <token>'
  return Authorization?.split(' ')[1] || '';
}

export class PDFService {
  // Use Cloud Run PDF processor URL in production
  private static readonly PDF_SERVICE_URL = process.env.PDF_SERVICE_URL || 'https://pdf-processor-956727780312.us-central1.run.app';
  private static readonly TIMEOUT = 300000; // 5 minutes

  /**
   * Process a PDF file using the Python PDF service
   */
  static async processPDF(
    fileBuffer: Buffer,
    fileName: string,
    userId: string,
    processingId: string
  ): Promise<PDFProcessingResult> {
    try {
      console.log(`📄 Sending PDF to Python service: ${fileName}`);

      // Create form data
      const FormData = require('form-data');
      const form = new FormData();
      form.append('file', fileBuffer, {
        filename: fileName,
        contentType: 'application/pdf'
      });
      form.append('userId', userId);

      // Get ID token for Cloud Run
      const audience = this.PDF_SERVICE_URL;
      const idToken = await getCloudRunIdToken(audience);
      // Make request to Python PDF service
      const response = await axios.post(
        `${this.PDF_SERVICE_URL}/api/v1/process`,
        form,
        {
          headers: {
            ...form.getHeaders(),
            'X-User-Id': userId,
            'X-Processing-Id': processingId,
            'Authorization': `Bearer ${idToken}`
          },
          timeout: this.TIMEOUT,
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        }
      );

      if (response.data.success) {
        console.log(`✅ PDF processed successfully: ${response.data.data.extraction_method}`);
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'PDF processing failed');
      }

    } catch (error) {
      console.error('❌ PDF service error:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          throw new Error('PDF service is not available. Please ensure the Python PDF processor is running.');
        }
        if (error.response) {
          throw new Error(`PDF service error: ${error.response.data?.error || error.message}`);
        }
      }
      
      throw new Error(`PDF processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process multiple PDFs in batch
   */
  static async processPDFBatch(
    files: Array<{ buffer: Buffer; filename: string }>,
    userId: string,
    batchId: string
  ): Promise<PDFBatchResult> {
    try {
      console.log(`📦 Sending batch to Python service: ${batchId} with ${files.length} files`);

      // Create form data
      const FormData = require('form-data');
      const form = new FormData();
      
      files.forEach((file, _index) => {
        form.append('files', file.buffer, {
          filename: file.filename,
          contentType: 'application/pdf'
        });
      });
      
      form.append('userId', userId);
      form.append('batchId', batchId);

      // Get ID token for Cloud Run
      const audience = this.PDF_SERVICE_URL;
      const idToken = await getCloudRunIdToken(audience);
      // Make request to Python PDF service
      const response = await axios.post(
        `${this.PDF_SERVICE_URL}/api/v1/batch`,
        form,
        {
          headers: {
            ...form.getHeaders(),
            'X-User-Id': userId,
            'X-Batch-Id': batchId,
            'Authorization': `Bearer ${idToken}`
          },
          timeout: this.TIMEOUT * 2, // Longer timeout for batch processing
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        }
      );

      if (response.data.success) {
        console.log(`✅ Batch processed successfully: ${response.data.data.success_count}/${response.data.data.total_files}`);
        return response.data;
      } else {
        throw new Error(response.data.error || 'Batch processing failed');
      }

    } catch (error) {
      console.error('❌ PDF batch service error:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          throw new Error('PDF service is not available. Please ensure the Python PDF processor is running.');
        }
        if (error.response) {
          throw new Error(`PDF service error: ${error.response.data?.error || error.message}`);
        }
      }
      
      throw new Error(`Batch processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if PDF service is healthy
   */
  static async healthCheck(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.PDF_SERVICE_URL}/health`, {
        timeout: 5000
      });
      return response.data.success === true;
    } catch (error) {
      console.error('PDF service health check failed:', error);
      return false;
    }
  }

  /**
   * Convert base64 image data to Buffer
   */
  static base64ToBuffer(base64Data: string): Buffer {
    return Buffer.from(base64Data, 'base64');
  }

  /**
   * Get the best extraction method for a file
   */
  static getExtractionMethod(mimeType: string): 'pdf_service' | 'gemini_ocr' {
    if (mimeType === 'application/pdf') {
      return 'pdf_service';
    }
    return 'gemini_ocr';
  }
} 