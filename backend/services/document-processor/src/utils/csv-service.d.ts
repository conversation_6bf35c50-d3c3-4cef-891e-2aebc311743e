export declare class CSVService {
    /**
     * Parse CSV file and convert to structured text
     */
    static parseCSV(fileBuffer: Buffer): Promise<string>;
    /**
     * Validate CSV structure for financial data
     */
    static validateFinancialCSV(records: any[]): {
        isValid: boolean;
        errors: string[];
    };
    /**
     * Extract transaction data from CSV records
     */
    static extractTransactions(records: any[]): any[];
}
//# sourceMappingURL=csv-service.d.ts.map