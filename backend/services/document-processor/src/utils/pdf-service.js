"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PDFService = void 0;
const axios_1 = __importDefault(require("axios"));
class PDFService {
    /**
     * Process a PDF file using the Python PDF service
     */
    static async processPDF(fileBuffer, fileName, userId, processingId) {
        try {
            console.log(`📄 Sending PDF to Python service: ${fileName}`);
            // Create form data
            const FormData = require('form-data');
            const form = new FormData();
            form.append('file', fileBuffer, {
                filename: fileName,
                contentType: 'application/pdf'
            });
            form.append('userId', userId);
            // Make request to Python PDF service
            const response = await axios_1.default.post(`${this.PDF_SERVICE_URL}/api/v1/process`, form, {
                headers: {
                    ...form.getHeaders(),
                    'X-User-Id': userId,
                    'X-Processing-Id': processingId
                },
                timeout: this.TIMEOUT,
                maxContentLength: Infinity,
                maxBodyLength: Infinity
            });
            if (response.data.success) {
                console.log(`✅ PDF processed successfully: ${response.data.data.extraction_method}`);
                return response.data.data;
            }
            else {
                throw new Error(response.data.error || 'PDF processing failed');
            }
        }
        catch (error) {
            console.error('❌ PDF service error:', error);
            if (axios_1.default.isAxiosError(error)) {
                if (error.code === 'ECONNREFUSED') {
                    throw new Error('PDF service is not available. Please ensure the Python PDF processor is running.');
                }
                if (error.response) {
                    throw new Error(`PDF service error: ${error.response.data?.error || error.message}`);
                }
            }
            throw new Error(`PDF processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Process multiple PDFs in batch
     */
    static async processPDFBatch(files, userId, batchId) {
        try {
            console.log(`📦 Sending batch to Python service: ${batchId} with ${files.length} files`);
            // Create form data
            const FormData = require('form-data');
            const form = new FormData();
            files.forEach((file, _index) => {
                form.append('files', file.buffer, {
                    filename: file.filename,
                    contentType: 'application/pdf'
                });
            });
            form.append('userId', userId);
            form.append('batchId', batchId);
            // Make request to Python PDF service
            const response = await axios_1.default.post(`${this.PDF_SERVICE_URL}/api/v1/batch`, form, {
                headers: {
                    ...form.getHeaders(),
                    'X-User-Id': userId,
                    'X-Batch-Id': batchId
                },
                timeout: this.TIMEOUT * 2, // Longer timeout for batch processing
                maxContentLength: Infinity,
                maxBodyLength: Infinity
            });
            if (response.data.success) {
                console.log(`✅ Batch processed successfully: ${response.data.data.success_count}/${response.data.data.total_files}`);
                return response.data;
            }
            else {
                throw new Error(response.data.error || 'Batch processing failed');
            }
        }
        catch (error) {
            console.error('❌ PDF batch service error:', error);
            if (axios_1.default.isAxiosError(error)) {
                if (error.code === 'ECONNREFUSED') {
                    throw new Error('PDF service is not available. Please ensure the Python PDF processor is running.');
                }
                if (error.response) {
                    throw new Error(`PDF service error: ${error.response.data?.error || error.message}`);
                }
            }
            throw new Error(`Batch processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Check if PDF service is healthy
     */
    static async healthCheck() {
        try {
            const response = await axios_1.default.get(`${this.PDF_SERVICE_URL}/health`, {
                timeout: 5000
            });
            return response.data.success === true;
        }
        catch (error) {
            console.error('PDF service health check failed:', error);
            return false;
        }
    }
    /**
     * Convert base64 image data to Buffer
     */
    static base64ToBuffer(base64Data) {
        return Buffer.from(base64Data, 'base64');
    }
    /**
     * Get the best extraction method for a file
     */
    static getExtractionMethod(mimeType) {
        if (mimeType === 'application/pdf') {
            return 'pdf_service';
        }
        return 'gemini_ocr';
    }
}
exports.PDFService = PDFService;
// Use Cloud Run PDF processor URL in production
PDFService.PDF_SERVICE_URL = process.env.PDF_SERVICE_URL || 'https://pdf-processor-956727780312.us-central1.run.app';
PDFService.TIMEOUT = 300000; // 5 minutes
//# sourceMappingURL=pdf-service.js.map