"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseService = void 0;
exports.initializeSupabase = initializeSupabase;
const supabase_js_1 = require("@supabase/supabase-js");
const supabasePublishableKey = process.env.SUPABASE_PUBLISHABLE_KEY;
const supabaseSecretKey = process.env.SUPABASE_SECRET_KEY;
// Export function for backward compatibility
function initializeSupabase() {
    SupabaseService.initialize();
}
class SupabaseService {
    static initialize() {
        const supabaseUrl = process.env.SUPABASE_URL;
        this.client = (0, supabase_js_1.createClient)(supabaseUrl, supabasePublishableKey);
        this.serviceClient = (0, supabase_js_1.createClient)(supabaseUrl, supabaseSecretKey);
    }
    static getClient() {
        return this.client;
    }
    // Add or update a category for a user if not present
    static async upsertCategory(userId, name, color, icon) {
        if (!name)
            return;
        await this.client.from('categories').upsert({
            user_id: userId,
            name,
            color: color || '#3B82F6',
            icon: icon || null
        }, { onConflict: 'user_id,name' });
    }
    // Add or update a vendor for a user if not present
    static async upsertVendor(userId, name, categoryId, normalizedName) {
        if (!name)
            return;
        await this.client.from('vendors').upsert({
            user_id: userId,
            name,
            normalized_name: (normalizedName || name).toLowerCase().trim(),
            category_id: categoryId || null
        }, { onConflict: 'user_id,normalized_name' });
    }
    // Alias for insertTransactions to match controller expectations
    static async addTransactions(transactions) {
        if (transactions.length === 0)
            return [];
        const userId = transactions[0].user_id;
        const result = await this.insertTransactions(transactions, userId);
        if (!result.success) {
            throw new Error(`Failed to insert transactions: ${result.errors?.join(', ') || 'Unknown error'}`);
        }
        // Return the transactions with IDs
        return transactions.map((tx, index) => ({
            ...tx,
            id: result.transactionIds?.[index] || `temp_${Date.now()}_${index}`
        }));
    }
    // Get all transactions for a user
    static async getTransactions(userId) {
        try {
            const { data, error } = await this.client
                .from('transactions')
                .select('*')
                .eq('user_id', userId)
                .order('date', { ascending: false });
            if (error) {
                console.error('Error fetching transactions:', error);
                throw error;
            }
            return data || [];
        }
        catch (error) {
            console.error('Failed to fetch transactions:', error);
            throw error;
        }
    }
    // Update a transaction
    static async updateTransaction(id, updates) {
        try {
            const { data, error } = await this.client
                .from('transactions')
                .update(updates)
                .eq('id', id)
                .select()
                .single();
            if (error) {
                console.error('Error updating transaction:', error);
                throw error;
            }
            return data;
        }
        catch (error) {
            console.error('Failed to update transaction:', error);
            throw error;
        }
    }
    // Delete a transaction
    static async deleteTransaction(id) {
        try {
            const { error } = await this.client
                .from('transactions')
                .delete()
                .eq('id', id);
            if (error) {
                console.error('Error deleting transaction:', error);
                throw error;
            }
        }
        catch (error) {
            console.error('Failed to delete transaction:', error);
            throw error;
        }
    }
    // When inserting transactions, also upsert categories/vendors if present
    static async insertTransactions(transactions, userId) {
        // Upsert categories and vendors for each transaction (additive, non-breaking)
        for (const tx of transactions) {
            if (tx.category) {
                await this.upsertCategory(userId, tx.category);
            }
            if (tx.vendor) {
                await this.upsertVendor(userId, tx.vendor);
            }
        }
        const batchSize = 100;
        let insertedCount = 0;
        let duplicateCount = 0;
        let errorCount = 0;
        const errors = [];
        let transactionIds = [];
        for (let i = 0; i < transactions.length; i += batchSize) {
            const batch = transactions.slice(i, i + batchSize);
            try {
                // Use upsert with onConflict for deduplication
                const { data, error } = await this.client
                    .from('transactions')
                    .upsert(batch, {
                    onConflict: 'user_id,fingerprint',
                    ignoreDuplicates: true
                })
                    .select('id');
                if (error)
                    throw error;
                insertedCount += data?.length || 0;
                duplicateCount += batch.length - (data?.length || 0);
                transactionIds.push(...(data?.map((tx) => tx.id) || []));
            }
            catch (error) {
                errorCount += batch.length;
                errors.push(error instanceof Error ? error.message : 'Unknown error');
            }
        }
        return {
            id: `batch_${Date.now()}`,
            created_at: new Date().toISOString(),
            success: errorCount === 0,
            insertedCount,
            duplicateCount,
            errorCount,
            errors,
            transactionIds
        };
    }
    static async getTransactionStats(userId, startDate, endDate) {
        try {
            let query = this.client
                .from('transactions')
                .select('amount, type, category')
                .eq('user_id', userId);
            if (startDate && endDate) {
                query = query.gte('date', startDate).lte('date', endDate);
            }
            const { data, error } = await query;
            if (error) {
                console.error('Error getting transaction stats:', error);
                return {
                    totalIncome: 0,
                    totalExpenses: 0,
                    netCashFlow: 0,
                    transactionCount: 0,
                    categoryBreakdown: {},
                };
            }
            const transactions = data || [];
            const totalIncome = transactions
                .filter((t) => t.type === 'credit')
                .reduce((sum, t) => sum + (t.amount || 0), 0);
            const totalExpenses = transactions
                .filter((t) => t.type === 'debit')
                .reduce((sum, t) => sum + (t.amount || 0), 0);
            const categoryBreakdown = transactions.reduce((acc, tx) => {
                const category = tx.category || 'Uncategorized';
                acc[category] = (acc[category] || 0) + (tx.amount || 0);
                return acc;
            }, {});
            return {
                totalIncome,
                totalExpenses,
                netCashFlow: totalIncome - totalExpenses,
                transactionCount: transactions.length,
                categoryBreakdown,
            };
        }
        catch (error) {
            console.error('Error getting transaction stats:', error);
            return {
                totalIncome: 0,
                totalExpenses: 0,
                netCashFlow: 0,
                transactionCount: 0,
                categoryBreakdown: {},
            };
        }
    }
    static async checkTransactionExists(fingerprint, userId) {
        try {
            const { data, error } = await this.client
                .from('transactions')
                .select('id')
                .eq('fingerprint', fingerprint)
                .eq('user_id', userId)
                .limit(1);
            if (error) {
                console.error('Error checking transaction existence:', error);
                return false;
            }
            return (data?.length || 0) > 0;
        }
        catch (error) {
            console.error('Error checking transaction existence:', error);
            return false;
        }
    }
    static async autoCategorizeTransactions(transactionIds, userId) {
        // For now, just set category to 'Auto' for all selected transactions. In the future, call AI service here.
        try {
            const { data, error } = await this.client
                .from('transactions')
                .update({ category: 'Auto' })
                .in('id', transactionIds)
                .eq('user_id', userId)
                .select();
            if (error) {
                console.error('Error auto-categorizing transactions:', error);
                throw error;
            }
            return data || [];
        }
        catch (error) {
            console.error('Failed to auto-categorize transactions:', error);
            throw error;
        }
    }
    // New: Analytics - get spending by category
    static async getSpendingByCategory(userId, startDate, endDate) {
        let query = this.client
            .from('transactions')
            .select('category, amount, type')
            .eq('user_id', userId)
            .eq('type', 'debit');
        if (startDate)
            query = query.gte('date', startDate);
        if (endDate)
            query = query.lte('date', endDate);
        const { data, error } = await query;
        if (error)
            throw error;
        // Group by category
        const categoryTotals = (data || []).reduce((acc, transaction) => {
            const category = transaction.category || 'Uncategorized';
            acc[category] = (acc[category] || 0) + transaction.amount;
            return acc;
        }, {});
        return Object.entries(categoryTotals)
            .map(([category, amount]) => ({ category, amount }))
            .sort((a, b) => b.amount - a.amount);
    }
}
exports.SupabaseService = SupabaseService;
//# sourceMappingURL=supabase.js.map