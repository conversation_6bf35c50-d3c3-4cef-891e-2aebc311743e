import { parse } from 'csv-parse/sync';

export class CSVService {
  /**
   * Parse CSV file and convert to structured text
   */
  static async parseCSV(fileBuffer: Buffer): Promise<string> {
    try {
      console.log('📊 Parsing CSV file...');

      // Parse CSV with headers
      const records = parse(fileBuffer.toString(), {
        columns: true, // Use first row as headers
        skip_empty_lines: true,
        trim: true
      });

      if (!records || records.length === 0) {
        throw new Error('No data found in CSV file');
      }

      // Get headers from first record
      const headers = Object.keys(records[0] as Record<string, any>);
      
      // Convert to structured text format
      let structuredText = 'CSV Data:\n\n';
      
      // Add headers
      structuredText += `Headers: ${headers.join(', ')}\n\n`;
      
      // Add data rows
      records.forEach((record: any, index: number) => {
        structuredText += `Row ${index + 1}:\n`;
        headers.forEach(header => {
          const value = record[header] || '';
          structuredText += `  ${header}: ${value}\n`;
        });
        structuredText += '\n';
      });

      console.log(`✅ CSV parsed successfully: ${records.length} rows, ${headers.length} columns`);
      return structuredText;

    } catch (error) {
      console.error('❌ CSV parsing failed:', error);
      throw new Error(`CSV parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate CSV structure for financial data
   */
  static validateFinancialCSV(records: any[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!records || records.length === 0) {
      errors.push('No data found in CSV');
      return { isValid: false, errors };
    }

    const firstRecord = records[0];
    const headers = Object.keys(firstRecord as Record<string, any>);
    
    // Check for common financial fields
    const requiredFields = ['date', 'amount', 'description'];

    
    const missingRequired = requiredFields.filter(field => 
      !headers.some(header => header.toLowerCase().includes(field))
    );
    
    if (missingRequired.length > 0) {
      errors.push(`Missing required fields: ${missingRequired.join(', ')}`);
    }

    // Check for date format consistency
    const dateFields = headers.filter(header => 
      header.toLowerCase().includes('date')
    );
    
    if (dateFields.length === 0) {
      errors.push('No date field found');
    }

    // Check for amount/numeric fields
    const amountFields = headers.filter(header => 
      header.toLowerCase().includes('amount') || 
      header.toLowerCase().includes('balance') ||
      header.toLowerCase().includes('value')
    );
    
    if (amountFields.length === 0) {
      errors.push('No amount/balance field found');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Extract transaction data from CSV records
   */
  static extractTransactions(records: any[]): any[] {
    const transactions: any[] = [];
    
    records.forEach((record, index) => {
      const transaction: any = {
        row: index + 1
      };

      // Map common field names
      Object.keys(record as Record<string, any>).forEach(key => {
        const lowerKey = key.toLowerCase();
        
        if (lowerKey.includes('date')) {
          transaction.date = record[key];
        } else if (lowerKey.includes('amount') || lowerKey.includes('value')) {
          transaction.amount = parseFloat(record[key]) || 0;
        } else if (lowerKey.includes('description') || lowerKey.includes('narration') || lowerKey.includes('details')) {
          transaction.description = record[key];
        } else if (lowerKey.includes('type')) {
          transaction.type = record[key];
        } else if (lowerKey.includes('category')) {
          transaction.category = record[key];
        } else if (lowerKey.includes('reference') || lowerKey.includes('ref')) {
          transaction.reference = record[key];
        } else if (lowerKey.includes('account')) {
          transaction.account = record[key];
        } else if (lowerKey.includes('balance')) {
          transaction.balance = parseFloat(record[key]) || 0;
        } else {
          // Store other fields as additional data
          transaction[key] = record[key];
        }
      });

      // Only add if we have essential fields
      if (transaction.date && transaction.amount && transaction.description) {
        transactions.push(transaction);
      }
    });

    return transactions;
  }
} 