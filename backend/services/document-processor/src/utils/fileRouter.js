"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileRouter = void 0;
const pdf_service_1 = require("./pdf-service");
const gemini_1 = require("./gemini");
const csv_service_1 = require("./csv-service");
const excel_service_1 = require("./excel-service");
const uuid_1 = require("uuid");
class FileRouter {
    /**
     * Main routing logic - decides between different processing methods
     */
    static async routeDocument(fileBuffer, mimeType, fileName, userId, processingId) {
        const startTime = Date.now();
        try {
            // Route based on file type
            if (mimeType === 'application/pdf') {
                return await this.processPDF(fileBuffer, fileName, userId, processingId);
            }
            else if (mimeType === 'text/csv') {
                return await this.processCSV(fileBuffer, fileName, processingId);
            }
            else if (mimeType === 'application/vnd.ms-excel' ||
                mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
                return await this.processExcel(fileBuffer, fileName, processingId);
            }
            else {
                return await this.processImage(fileBuffer, mimeType, fileName, processingId);
            }
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            console.error('❌ Document routing failed:', error);
            return {
                success: false,
                text: '',
                method: 'gemini_ocr',
                metadata: {
                    processingTime,
                    textLength: 0,
                    error: error instanceof Error ? error.message : 'Unknown error'
                }
            };
        }
    }
    /**
     * Process PDF files with smart routing
     */
    static async processPDF(fileBuffer, fileName, userId, processingId) {
        const startTime = Date.now();
        try {
            console.log(`📄 Routing PDF: ${fileName}`);
            // Step 1: Try Python PDF service first
            const pdfResult = await pdf_service_1.PDFService.processPDF(fileBuffer, fileName, userId, processingId);
            if (pdfResult.has_text && pdfResult.text && pdfResult.text.length > this.TEXT_THRESHOLD) {
                // Fast path: pdfplumber extracted text successfully
                const processingTime = Date.now() - startTime;
                console.log(`✅ PDF text extracted with pdfplumber: ${pdfResult.text.length} characters`);
                return {
                    success: true,
                    text: pdfResult.text,
                    method: 'pdfplumber',
                    metadata: {
                        processingTime,
                        textLength: pdfResult.text.length,
                        pagesProcessed: pdfResult.text.split('--- Page').length - 1,
                        confidence: 0.95 // High confidence for pdfplumber
                    }
                };
            }
            else if (pdfResult.images && pdfResult.images.length > 0) {
                // Fallback path: Convert PDF to images and use OCR
                console.log(`🔄 PDF converted to ${pdfResult.images.length} images, using Gemini OCR`);
                const imageTexts = await Promise.all(pdfResult.images.map(async (image, _index) => {
                    try {
                        const imageBuffer = pdf_service_1.PDFService.base64ToBuffer(image.image_data);
                        const base64Image = imageBuffer.toString('base64');
                        const prompt = `
              Extract ALL text from this financial document image (Page ${image.page_number}). Be thorough and include:
              - All numbers, amounts, and currency symbols
              - Dates and timestamps
              - Account numbers and reference codes
              - Names, addresses, and contact information
              - Transaction descriptions and merchant details
              - Any codes, IDs, or reference numbers
              
              Return ONLY the extracted text, preserving the original formatting as much as possible.
              Do not add any analysis or interpretation - just extract the raw text.
              `;
                        const response = await gemini_1.GeminiService.generateText(prompt, { data: base64Image, mimeType: image.mime_type }, 'gemini-1.5-flash');
                        if (!response.success) {
                            console.warn(`OCR failed for page ${image.page_number}: ${response.error}`);
                            return `[OCR failed for page ${image.page_number}]`;
                        }
                        return response.data || '';
                    }
                    catch (error) {
                        console.error(`Error processing page ${image.page_number}:`, error);
                        return `[Error processing page ${image.page_number}]`;
                    }
                }));
                const combinedText = imageTexts.join('\n\n--- Page Break ---\n\n');
                const processingTime = Date.now() - startTime;
                return {
                    success: true,
                    text: combinedText,
                    method: 'image_conversion',
                    metadata: {
                        processingTime,
                        textLength: combinedText.length,
                        pagesProcessed: pdfResult.images.length,
                        confidence: 0.8 // Medium confidence for OCR
                    }
                };
            }
            else {
                throw new Error('PDF processing returned no text or images');
            }
        }
        catch (error) {
            console.error('❌ PDF service failed, falling back to direct Gemini OCR:', error);
            // Ultimate fallback: Direct Gemini OCR on PDF
            return await this.fallbackToDirectOCR(fileBuffer, fileName, startTime);
        }
    }
    /**
     * Process CSV files
     */
    static async processCSV(fileBuffer, fileName, _processingId) {
        const startTime = Date.now();
        try {
            console.log(`📊 Processing CSV: ${fileName}`);
            const csvText = await csv_service_1.CSVService.parseCSV(fileBuffer);
            const processingTime = Date.now() - startTime;
            return {
                success: true,
                text: csvText,
                method: 'csv_parser',
                metadata: {
                    processingTime,
                    textLength: csvText.length,
                    rowsProcessed: csvText.split('\n').length - 1, // Subtract header
                    confidence: 0.98 // Very high confidence for CSV parsing
                }
            };
        }
        catch (error) {
            console.error('❌ CSV processing failed:', error);
            const processingTime = Date.now() - startTime;
            return {
                success: false,
                text: '',
                method: 'csv_parser',
                metadata: {
                    processingTime,
                    textLength: 0,
                    error: error instanceof Error ? error.message : 'CSV processing failed'
                }
            };
        }
    }
    /**
     * Process Excel files
     */
    static async processExcel(fileBuffer, fileName, _processingId) {
        const startTime = Date.now();
        try {
            console.log(`📈 Processing Excel: ${fileName}`);
            const excelText = await excel_service_1.ExcelService.parseExcel(fileBuffer);
            const processingTime = Date.now() - startTime;
            return {
                success: true,
                text: excelText,
                method: 'excel_parser',
                metadata: {
                    processingTime,
                    textLength: excelText.length,
                    sheetsProcessed: excelText.split('--- Sheet').length - 1,
                    confidence: 0.95 // High confidence for Excel parsing
                }
            };
        }
        catch (error) {
            console.error('❌ Excel processing failed:', error);
            const processingTime = Date.now() - startTime;
            return {
                success: false,
                text: '',
                method: 'excel_parser',
                metadata: {
                    processingTime,
                    textLength: 0,
                    error: error instanceof Error ? error.message : 'Excel processing failed'
                }
            };
        }
    }
    /**
     * Process image files directly with Gemini OCR
     */
    static async processImage(fileBuffer, mimeType, fileName, _processingId) {
        const startTime = Date.now();
        try {
            console.log(`🖼️ Processing image with Gemini OCR: ${fileName}`);
            const base64Image = fileBuffer.toString('base64');
            const prompt = `
      Extract ALL text from this financial document image. Be thorough and include:
      - All numbers, amounts, and currency symbols
      - Dates and timestamps
      - Account numbers and reference codes
      - Names, addresses, and contact information
      - Transaction descriptions and merchant details
      - Any codes, IDs, or reference numbers
      
      Return ONLY the extracted text, preserving the original formatting as much as possible.
      Do not add any analysis or interpretation - just extract the raw text.
      `;
            const response = await gemini_1.GeminiService.generateText(prompt, { data: base64Image, mimeType: mimeType }, 'gemini-1.5-flash');
            if (!response.success) {
                throw new Error(`OCR failed: ${response.error}`);
            }
            const processingTime = Date.now() - startTime;
            const extractedText = response.data || '';
            return {
                success: true,
                text: extractedText,
                method: 'gemini_ocr',
                metadata: {
                    processingTime,
                    textLength: extractedText.length,
                    confidence: 0.8 // Medium confidence for OCR
                }
            };
        }
        catch (error) {
            console.error('❌ Image processing failed:', error);
            const processingTime = Date.now() - startTime;
            return {
                success: false,
                text: '',
                method: 'gemini_ocr',
                metadata: {
                    processingTime,
                    textLength: 0,
                    error: error instanceof Error ? error.message : 'Image processing failed'
                }
            };
        }
    }
    /**
     * Fallback to direct OCR for PDFs
     */
    static async fallbackToDirectOCR(fileBuffer, fileName, startTime) {
        try {
            console.log(`🔄 Fallback: Direct OCR on PDF: ${fileName}`);
            const base64Image = fileBuffer.toString('base64');
            const prompt = `
      This is a PDF document converted to base64. Extract ALL text from this financial document. Be thorough and include:
      - All numbers, amounts, and currency symbols
      - Dates and timestamps
      - Account numbers and reference codes
      - Names, addresses, and contact information
      - Transaction descriptions and merchant details
      - Any codes, IDs, or reference numbers
      
      Return ONLY the extracted text, preserving the original formatting as much as possible.
      Do not add any analysis or interpretation - just extract the raw text.
      `;
            const response = await gemini_1.GeminiService.generateText(prompt, { data: base64Image, mimeType: 'application/pdf' }, 'gemini-1.5-flash');
            if (!response.success) {
                throw new Error(`Direct OCR failed: ${response.error}`);
            }
            const processingTime = Date.now() - startTime;
            const extractedText = response.data || '';
            return {
                success: true,
                text: extractedText,
                method: 'gemini_ocr',
                metadata: {
                    processingTime,
                    textLength: extractedText.length,
                    confidence: 0.7 // Lower confidence for direct OCR
                }
            };
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            console.error('❌ Direct OCR fallback failed:', error);
            return {
                success: false,
                text: '',
                method: 'gemini_ocr',
                metadata: {
                    processingTime,
                    textLength: 0,
                    error: error instanceof Error ? error.message : 'Direct OCR failed'
                }
            };
        }
    }
    /**
     * Log routing decision for analytics
     */
    static async logRoutingDecision(processingId, userId, fileName, result) {
        try {
            const log = {
                id: (0, uuid_1.v4)(),
                level: 'info',
                timestamp: new Date().toISOString(),
                message: `Text extraction completed using ${result.method}`,
                data: {
                    processingId,
                    userId,
                    fileName,
                    status: result.success ? 'completed' : 'failed',
                    stage: 'text_extraction',
                    textLength: result.metadata.textLength,
                    confidence: result.metadata.confidence || 0,
                    error: result.metadata.error
                }
            };
            // Log to console for now (can be extended to database)
            console.log(`📊 Routing decision logged:`, log);
        }
        catch (error) {
            console.error('Failed to log routing decision:', error);
        }
    }
    /**
     * Estimate processing cost based on method and text length
     */
    static estimateCost(method, _textLength) {
        const baseCost = 0.001; // Base cost per request
        switch (method) {
            case 'pdfplumber':
                return baseCost * 0.1; // Very low cost for pdfplumber
            case 'csv_parser':
                return baseCost * 0.1; // Very low cost for CSV parsing
            case 'excel_parser':
                return baseCost * 0.2; // Low cost for Excel parsing
            case 'image_conversion':
                return baseCost * 2; // Higher cost for image conversion + OCR
            case 'gemini_ocr':
                return baseCost * 3; // Highest cost for direct OCR
            default:
                return baseCost;
        }
    }
}
exports.FileRouter = FileRouter;
FileRouter.TEXT_THRESHOLD = 100; // Minimum characters to consider as "has text"
//# sourceMappingURL=fileRouter.js.map