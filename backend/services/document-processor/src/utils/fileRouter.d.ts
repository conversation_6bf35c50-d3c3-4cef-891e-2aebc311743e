export interface ExtractionResult {
    success: boolean;
    text: string;
    method: 'pdfplumber' | 'image_conversion' | 'gemini_ocr' | 'csv_parser' | 'excel_parser';
    metadata: {
        processingTime: number;
        textLength: number;
        pagesProcessed?: number;
        confidence?: number;
        error?: string;
        rowsProcessed?: number;
        sheetsProcessed?: number;
    };
}
export declare class FileRouter {
    private static readonly TEXT_THRESHOLD;
    /**
     * Main routing logic - decides between different processing methods
     */
    static routeDocument(fileBuffer: Buffer, mimeType: string, fileName: string, userId: string, processingId: string): Promise<ExtractionResult>;
    /**
     * Process PDF files with smart routing
     */
    private static processPDF;
    /**
     * Process CSV files
     */
    private static processCSV;
    /**
     * Process Excel files
     */
    private static processExcel;
    /**
     * Process image files directly with Gemini OCR
     */
    private static processImage;
    /**
     * Fallback to direct OCR for PDFs
     */
    private static fallbackToDirectOCR;
    /**
     * Log routing decision for analytics
     */
    static logRoutingDecision(processingId: string, userId: string, fileName: string, result: ExtractionResult): Promise<void>;
    /**
     * Estimate processing cost based on method and text length
     */
    static estimateCost(method: string, _textLength: number): number;
}
//# sourceMappingURL=fileRouter.d.ts.map