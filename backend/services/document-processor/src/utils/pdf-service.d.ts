export interface PDFProcessingResult {
    success: boolean;
    extraction_method: 'pdfplumber' | 'image_conversion';
    text?: string;
    images?: Array<{
        page_number: number;
        image_data: string;
        mime_type: string;
        width: number;
        height: number;
        size_bytes: number;
    }>;
    has_text: boolean;
    filename: string;
    user_id: string;
    processed_at: string;
}
export interface PDFBatchResult {
    success: boolean;
    data: {
        batch_id: string;
        total_files: number;
        success_count: number;
        error_count: number;
        results: Array<{
            filename: string;
            success: boolean;
            extraction_method?: 'pdfplumber' | 'image_conversion';
            text?: string;
            images?: any[];
            has_text?: boolean;
            error?: string;
        }>;
    };
    message: string;
}
export declare class PDFService {
    private static readonly PDF_SERVICE_URL;
    private static readonly TIMEOUT;
    /**
     * Process a PDF file using the Python PDF service
     */
    static processPDF(fileBuffer: Buffer, fileName: string, userId: string, processingId: string): Promise<PDFProcessingResult>;
    /**
     * Process multiple PDFs in batch
     */
    static processPDFBatch(files: Array<{
        buffer: Buffer;
        filename: string;
    }>, userId: string, batchId: string): Promise<PDFBatchResult>;
    /**
     * Check if PDF service is healthy
     */
    static healthCheck(): Promise<boolean>;
    /**
     * Convert base64 image data to Buffer
     */
    static base64ToBuffer(base64Data: string): Buffer;
    /**
     * Get the best extraction method for a file
     */
    static getExtractionMethod(mimeType: string): 'pdf_service' | 'gemini_ocr';
}
//# sourceMappingURL=pdf-service.d.ts.map