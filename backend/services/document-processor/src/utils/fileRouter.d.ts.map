{"version": 3, "file": "fileRouter.d.ts", "sourceRoot": "", "sources": ["fileRouter.ts"], "names": [], "mappings": "AAOA,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,OAAO,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,YAAY,GAAG,kBAAkB,GAAG,YAAY,GAAG,YAAY,GAAG,cAAc,CAAC;IACzF,QAAQ,EAAE;QACR,cAAc,EAAE,MAAM,CAAC;QACvB,UAAU,EAAE,MAAM,CAAC;QACnB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,eAAe,CAAC,EAAE,MAAM,CAAC;KAC1B,CAAC;CACH;AAED,qBAAa,UAAU;IACrB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAO;IAG7C;;OAEG;WACU,aAAa,CACxB,UAAU,EAAE,MAAM,EAClB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,MAAM,GACnB,OAAO,CAAC,gBAAgB,CAAC;IAgC5B;;OAEG;mBACkB,UAAU;IAkG/B;;OAEG;mBACkB,UAAU;IA0C/B;;OAEG;mBACkB,YAAY;IA0CjC;;OAEG;mBACkB,YAAY;IAmEjC;;OAEG;mBACkB,mBAAmB;IAgExC;;OAEG;WACU,kBAAkB,CAC7B,YAAY,EAAE,MAAM,EACpB,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,gBAAgB,GACvB,OAAO,CAAC,IAAI,CAAC;IA2BhB;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,MAAM;CAkBjE"}