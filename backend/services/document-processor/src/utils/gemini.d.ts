import { GeminiResponse } from '../types';
export declare class GeminiService {
    private static ai;
    private static readonly MAX_RETRIES;
    private static readonly RETRY_DELAY;
    private static readonly GEMINI_MODEL_MULTIMODAL;
    static initialize(): void;
    static generateContent(prompt: string, imageData?: {
        data: string;
        mimeType: string;
    }, model?: string, retries?: number): Promise<GeminiResponse>;
    private static shouldRetry;
    private static delay;
    static generateText(prompt: string, imageData?: {
        data: string;
        mimeType: string;
    }, _model?: string): Promise<GeminiResponse>;
    static estimateTokenCount(text: string): number;
    static estimateCost(inputTokens: number, outputTokens: number, _model?: string): number;
}
//# sourceMappingURL=gemini.d.ts.map