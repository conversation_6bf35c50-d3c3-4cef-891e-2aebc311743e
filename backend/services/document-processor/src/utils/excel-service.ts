import * as XLSX from 'xlsx';

export class ExcelService {
  /**
   * Parse Excel file and convert to structured text
   */
  static async parseExcel(fileBuffer: Buffer): Promise<string> {
    try {
      console.log('📈 Parsing Excel file...');

      // Read the workbook
      const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
      
      if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
        throw new Error('No sheets found in Excel file');
      }

      let structuredText = 'Excel Data:\n\n';
      
      // Process each sheet
      workbook.SheetNames.forEach((sheetName, sheetIndex) => {
        console.log(`📊 Processing sheet: ${sheetName}`);
        
        const worksheet = workbook.Sheets[sheetName];
        if (!worksheet) {
          structuredText += `Sheet ${sheetIndex + 1}: ${sheetName} - No data\n\n`;
          return;
        }
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (!jsonData || jsonData.length === 0) {
          structuredText += `Sheet ${sheetIndex + 1}: ${sheetName} - No data\n\n`;
          return;
        }

        structuredText += `--- Sheet ${sheetIndex + 1}: ${sheetName} ---\n\n`;
        
        // Get headers (first row)
        const headers = jsonData[0] as string[];
        structuredText += `Headers: ${headers.join(', ')}\n\n`;
        
        // Process data rows
        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i] as any[];
          if (row && row.length > 0) {
            structuredText += `Row ${i}:\n`;
            headers.forEach((header, colIndex) => {
              const value = row[colIndex] || '';
              structuredText += `  ${header}: ${value}\n`;
            });
            structuredText += '\n';
          }
        }
        
        structuredText += '\n';
      });

      console.log(`✅ Excel parsed successfully: ${workbook.SheetNames.length} sheets`);
      return structuredText;

    } catch (error) {
      console.error('❌ Excel parsing failed:', error);
      throw new Error(`Excel parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract transaction data from Excel workbook
   */
  static extractTransactions(workbook: XLSX.WorkBook): any[] {
    const transactions: any[] = [];
    
    workbook.SheetNames.forEach((sheetName, sheetIndex) => {
      const worksheet = workbook.Sheets[sheetName];
      if (!worksheet) return; // Need at least headers + 1 data row
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (!jsonData || jsonData.length < 2) return; // Need at least headers + 1 data row
      
      const headers = jsonData[0] as string[];
      
      // Process data rows
      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i] as any[];
        if (!row || row.length === 0) continue;
        
        const transaction: any = {
          sheet: sheetName,
          sheetIndex: sheetIndex + 1,
          row: i
        };

        // Map common field names
        headers.forEach((header, colIndex) => {
          if (!header) return;
          
          const lowerHeader = header.toLowerCase();
          const value = row[colIndex];
          
          if (lowerHeader.includes('date')) {
            transaction.date = this.formatExcelDate(value);
          } else if (lowerHeader.includes('amount') || lowerHeader.includes('value')) {
            transaction.amount = parseFloat(value) || 0;
          } else if (lowerHeader.includes('description') || lowerHeader.includes('narration') || lowerHeader.includes('details')) {
            transaction.description = value;
          } else if (lowerHeader.includes('type')) {
            transaction.type = value;
          } else if (lowerHeader.includes('category')) {
            transaction.category = value;
          } else if (lowerHeader.includes('reference') || lowerHeader.includes('ref')) {
            transaction.reference = value;
          } else if (lowerHeader.includes('account')) {
            transaction.account = value;
          } else if (lowerHeader.includes('balance')) {
            transaction.balance = parseFloat(value) || 0;
          } else if (lowerHeader.includes('debit')) {
            transaction.debit = parseFloat(value) || 0;
          } else if (lowerHeader.includes('credit')) {
            transaction.credit = parseFloat(value) || 0;
          } else {
            // Store other fields as additional data
            transaction[header] = value;
          }
        });

        // Only add if we have essential fields
        if (transaction.date && (transaction.amount || transaction.debit || transaction.credit) && transaction.description) {
          // If we have debit/credit but no amount, calculate amount
          if (!transaction.amount && (transaction.debit || transaction.credit)) {
            transaction.amount = transaction.debit || transaction.credit;
            transaction.type = transaction.debit ? 'debit' : 'credit';
          }
          // Fix: ensure all string fields are not undefined
          transaction.date = transaction.date || '';
          transaction.description = transaction.description || '';
          transaction.type = transaction.type || '';
          transaction.category = transaction.category || '';
          transaction.reference = transaction.reference || '';
          transaction.account = transaction.account || '';
          transactions.push(transaction);
        }
      }
    });

    return transactions;
  }

  /**
   * Format Excel date to standard format
   */
  private static formatExcelDate(value: any): string {
    if (!value) return '';
    if (typeof value === 'string') return value;
    if (typeof value === 'number') {
      const date = XLSX.SSF.parse_date_code(value);
      if (date) {
        return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')}`;
      }
      return '';
    }
    // Explicit type guard with null checking
    if (value instanceof Date && !isNaN(value.getTime())) {
      const isoString: string = value.toISOString();
      return isoString.split('T')[0];
    }
    return '';
  }

  /**
   * Validate Excel structure for financial data
   */
  static validateFinancialExcel(workbook: XLSX.WorkBook): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      errors.push('No sheets found in Excel file');
      return { isValid: false, errors };
    }

    let hasValidSheet = false;
    
    workbook.SheetNames.forEach((sheetName, sheetIndex) => {
      const worksheet = workbook.Sheets[sheetName];
      if (!worksheet) {
        errors.push(`Sheet ${sheetIndex + 1} (${sheetName}): No worksheet found`);
        return;
      }
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (!jsonData || jsonData.length < 2) {
        errors.push(`Sheet ${sheetIndex + 1} (${sheetName}): No data or insufficient rows`);
        return;
      }

      const headers = jsonData[0] as string[];
      if (!headers || headers.length === 0) {
        errors.push(`Sheet ${sheetIndex + 1} (${sheetName}): No headers found`);
        return;
      }

      // Check for common financial fields
      const requiredFields = ['date', 'amount', 'description'];

      
      const missingRequired = requiredFields.filter(field => 
        !headers.some(header => header && header.toLowerCase().includes(field))
      );
      
      if (missingRequired.length === 0) {
        hasValidSheet = true;
      } else {
        errors.push(`Sheet ${sheetIndex + 1} (${sheetName}): Missing required fields: ${missingRequired.join(', ')}`);
      }
    });

    if (!hasValidSheet) {
      errors.push('No sheet contains required financial fields (date, amount, description)');
    }

    return {
      isValid: hasValidSheet,
      errors
    };
  }

  /**
   * Get sheet information
   */
  static getSheetInfo(workbook: XLSX.WorkBook): Array<{
    name: string;
    index: number;
    rowCount: number;
    columnCount: number;
    hasData: boolean;
  }> {
    return workbook.SheetNames.map((sheetName, index) => {
      const worksheet = workbook.Sheets[sheetName];
      if (!worksheet) {
        return {
          name: sheetName,
          index: index + 1,
          rowCount: 0,
          columnCount: 0,
          hasData: false
        };
      }
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      return {
        name: sheetName,
        index: index + 1,
        rowCount: jsonData ? jsonData.length : 0,
        columnCount: jsonData && jsonData[0] ? (jsonData[0] as any[]).length : 0,
        hasData: jsonData ? jsonData.length > 1 : false
      };
    });
  }
} 