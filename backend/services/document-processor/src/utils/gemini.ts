import { GoogleGenAI } from '@google/genai';
import { GeminiResponse } from '../types';

export class GeminiService {
  private static ai: GoogleGenAI;
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAY = 1000; // 1 second
  private static readonly GEMINI_MODEL_MULTIMODAL = 'gemini-1.5-flash';

  static initialize() {
    const apiKey = process.env.GEMINI_API_KEY!;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }
    this.ai = new GoogleGenAI({ apiKey });
  }

  static async generateContent(
    prompt: string,
    imageData?: { data: string; mimeType: string },
    model: string = 'gemini-1.5-flash',
    retries: number = 0
  ): Promise<GeminiResponse> {


    try {
      const modelInstance = this.ai.models.generateContent;

      const parts: any[] = [{ text: prompt }];
      if (imageData) {
        parts.push({
          inlineData: {
            data: imageData.data,
            mimeType: imageData.mimeType,
          },
        });
      }

      const result = await modelInstance({
        model: this.GEMINI_MODEL_MULTIMODAL,
        contents: {
          parts
        },
        config: { responseMimeType: "application/json" }
      });

      const response = result;
      const text = response.text || '';

      // Validate JSON response
      let parsedData;
      try {
        parsedData = JSON.parse(text);
      } catch (parseError) {
        const errorMessage = `Invalid JSON response from Gemini: ${text.slice(0, 300)}...`;
        console.error(errorMessage);
        
        // Retry if we haven't exceeded max retries
        if (retries < this.MAX_RETRIES) {
          console.log(`Retrying Gemini API call (attempt ${retries + 1}/${this.MAX_RETRIES})`);
          await this.delay(this.RETRY_DELAY * (retries + 1));
          return this.generateContent(prompt, imageData, model, retries + 1);
        }

        return {
          success: false,
          error: errorMessage,
          rawResponse: text,
        };
      }



      return {
        success: true,
        data: parsedData,
        rawResponse: text,
        tokenUsage: {
          input: this.estimateTokenCount(prompt),
          output: this.estimateTokenCount(text),
          total: this.estimateTokenCount(prompt) + this.estimateTokenCount(text),
          inputTokens: this.estimateTokenCount(prompt),
          outputTokens: this.estimateTokenCount(text),
          totalTokens: this.estimateTokenCount(prompt) + this.estimateTokenCount(text),
        },
      };

    } catch (error) {

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      console.error(`Gemini API error (attempt ${retries + 1}):`, errorMessage);

      // Retry on certain errors
      if (this.shouldRetry(error) && retries < this.MAX_RETRIES) {
        console.log(`Retrying Gemini API call (attempt ${retries + 1}/${this.MAX_RETRIES})`);
        await this.delay(this.RETRY_DELAY * (retries + 1));
        return this.generateContent(prompt, imageData, model, retries + 1);
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  private static shouldRetry(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    
    // Retry on network errors, rate limits, and temporary server errors
    return errorMessage.includes('network') ||
           errorMessage.includes('timeout') ||
           errorMessage.includes('rate limit') ||
           errorMessage.includes('quota') ||
           errorMessage.includes('temporary') ||
           errorMessage.includes('server error') ||
           error?.code === 'ECONNRESET' ||
           error?.code === 'ETIMEDOUT';
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static async generateText(
    prompt: string,
    imageData?: { data: string; mimeType: string },
    _model: string = 'gemini-1.5-flash'
  ): Promise<GeminiResponse> {
    try {
      const modelInstance = this.ai.models.generateContent;

      const parts: any[] = [{ text: prompt }];
      if (imageData) {
        parts.push({
          inlineData: {
            data: imageData.data,
            mimeType: imageData.mimeType,
          },
        });
      }

      const result = await modelInstance({
        model: this.GEMINI_MODEL_MULTIMODAL,
        contents: {
          parts
        }
      });

      const response = result;
      const text = response.text || '';

      return {
        success: true,
        data: text,
        rawResponse: text,
        tokenUsage: {
          input: this.estimateTokenCount(prompt),
          output: this.estimateTokenCount(text),
          total: this.estimateTokenCount(prompt) + this.estimateTokenCount(text),
          inputTokens: this.estimateTokenCount(prompt),
          outputTokens: this.estimateTokenCount(text),
          totalTokens: this.estimateTokenCount(prompt) + this.estimateTokenCount(text),
        },
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Gemini text generation error:', errorMessage);

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  static estimateTokenCount(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  static estimateCost(inputTokens: number, outputTokens: number, _model: string = 'gemini-1.5-flash'): number {
    // Pricing for Gemini 1.5 Flash (as of 2024)
    const inputPricePer1K = 0.075; // $0.075 per 1K input tokens
    const outputPricePer1K = 0.30; // $0.30 per 1K output tokens

    const inputCost = (inputTokens / 1000) * inputPricePer1K;
    const outputCost = (outputTokens / 1000) * outputPricePer1K;

    return inputCost + outputCost;
  }
} 