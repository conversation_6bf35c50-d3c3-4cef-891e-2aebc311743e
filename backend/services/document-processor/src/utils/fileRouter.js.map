{"version": 3, "file": "fileRouter.js", "sourceRoot": "", "sources": ["fileRouter.ts"], "names": [], "mappings": ";;;AAAA,+CAA2C;AAC3C,qCAAyC;AACzC,+CAA2C;AAC3C,mDAA+C;AAE/C,+BAAoC;AAiBpC,MAAa,UAAU;IAIrB;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa,CACxB,UAAkB,EAClB,QAAgB,EAChB,QAAgB,EAChB,MAAc,EACd,YAAoB;QAEpB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,2BAA2B;YAC3B,IAAI,QAAQ,KAAK,iBAAiB,EAAE,CAAC;gBACnC,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;YAC3E,CAAC;iBAAM,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;gBACnC,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;YACnE,CAAC;iBAAM,IAAI,QAAQ,KAAK,0BAA0B;gBACvC,QAAQ,KAAK,mEAAmE,EAAE,CAAC;gBAC5F,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;YACrE,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAEnD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE;oBACR,cAAc;oBACd,UAAU,EAAE,CAAC;oBACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;iBAChE;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,UAAU,CAC7B,UAAkB,EAClB,QAAgB,EAChB,MAAc,EACd,YAAoB;QAEpB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YAE3C,uCAAuC;YACvC,MAAM,SAAS,GAAG,MAAM,wBAAU,CAAC,UAAU,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;YAE1F,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxF,oDAAoD;gBACpD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC9C,OAAO,CAAC,GAAG,CAAC,yCAAyC,SAAS,CAAC,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC;gBAEzF,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,MAAM,EAAE,YAAY;oBACpB,QAAQ,EAAE;wBACR,cAAc;wBACd,UAAU,EAAE,SAAS,CAAC,IAAI,CAAC,MAAM;wBACjC,cAAc,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC;wBAC3D,UAAU,EAAE,IAAI,CAAC,iCAAiC;qBACnD;iBACF,CAAC;YACJ,CAAC;iBAAM,IAAI,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3D,mDAAmD;gBACnD,OAAO,CAAC,GAAG,CAAC,uBAAuB,SAAS,CAAC,MAAM,CAAC,MAAM,2BAA2B,CAAC,CAAC;gBAEvF,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,GAAG,CAClC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;oBAC3C,IAAI,CAAC;wBACH,MAAM,WAAW,GAAG,wBAAU,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;wBAChE,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBAEnD,MAAM,MAAM,GAAG;0EAC6C,KAAK,CAAC,WAAW;;;;;;;;;;eAU5E,CAAC;wBAEF,MAAM,QAAQ,GAAG,MAAM,sBAAa,CAAC,YAAY,CAC/C,MAAM,EACN,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,CAAC,SAAS,EAAE,EAChD,kBAAkB,CACnB,CAAC;wBAEF,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;4BACtB,OAAO,CAAC,IAAI,CAAC,uBAAuB,KAAK,CAAC,WAAW,KAAK,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;4BAC5E,OAAO,wBAAwB,KAAK,CAAC,WAAW,GAAG,CAAC;wBACtD,CAAC;wBAED,OAAO,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;oBAC7B,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,KAAK,CAAC,WAAW,GAAG,EAAE,KAAK,CAAC,CAAC;wBACpE,OAAO,0BAA0B,KAAK,CAAC,WAAW,GAAG,CAAC;oBACxD,CAAC;gBACH,CAAC,CAAC,CACH,CAAC;gBAEF,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBACnE,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAE9C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,YAAY;oBAClB,MAAM,EAAE,kBAAkB;oBAC1B,QAAQ,EAAE;wBACR,cAAc;wBACd,UAAU,EAAE,YAAY,CAAC,MAAM;wBAC/B,cAAc,EAAE,SAAS,CAAC,MAAM,CAAC,MAAM;wBACvC,UAAU,EAAE,GAAG,CAAC,4BAA4B;qBAC7C;iBACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;YAEjF,8CAA8C;YAC9C,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,UAAU,CAC7B,UAAkB,EAClB,QAAgB,EAChB,aAAqB;QAErB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;YAE9C,MAAM,OAAO,GAAG,MAAM,wBAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE;oBACR,cAAc;oBACd,UAAU,EAAE,OAAO,CAAC,MAAM;oBAC1B,aAAa,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,kBAAkB;oBACjE,UAAU,EAAE,IAAI,CAAC,uCAAuC;iBACzD;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE;oBACR,cAAc;oBACd,UAAU,EAAE,CAAC;oBACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAuB;iBACxE;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,YAAY,CAC/B,UAAkB,EAClB,QAAgB,EAChB,aAAqB;QAErB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;YAEhD,MAAM,SAAS,GAAG,MAAM,4BAAY,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,cAAc;gBACtB,QAAQ,EAAE;oBACR,cAAc;oBACd,UAAU,EAAE,SAAS,CAAC,MAAM;oBAC5B,eAAe,EAAE,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC;oBACxD,UAAU,EAAE,IAAI,CAAC,oCAAoC;iBACtD;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,cAAc;gBACtB,QAAQ,EAAE;oBACR,cAAc;oBACd,UAAU,EAAE,CAAC;oBACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB;iBAC1E;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,YAAY,CAC/B,UAAkB,EAClB,QAAgB,EAChB,QAAgB,EAChB,aAAqB;QAErB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,QAAQ,EAAE,CAAC,CAAC;YAEjE,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG;;;;;;;;;;;OAWd,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,sBAAa,CAAC,YAAY,CAC/C,MAAM,EACN,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,EACzC,kBAAkB,CACnB,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,eAAe,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;YACnD,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;YAE1C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,aAAa;gBACnB,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE;oBACR,cAAc;oBACd,UAAU,EAAE,aAAa,CAAC,MAAM;oBAChC,UAAU,EAAE,GAAG,CAAC,4BAA4B;iBAC7C;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE;oBACR,cAAc;oBACd,UAAU,EAAE,CAAC;oBACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB;iBAC1E;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,mBAAmB,CACtC,UAAkB,EAClB,QAAgB,EAChB,SAAiB;QAEjB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAC;YAE3D,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG;;;;;;;;;;;OAWd,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,sBAAa,CAAC,YAAY,CAC/C,MAAM,EACN,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,iBAAiB,EAAE,EAClD,kBAAkB,CACnB,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;YAE1C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,aAAa;gBACnB,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE;oBACR,cAAc;oBACd,UAAU,EAAE,aAAa,CAAC,MAAM;oBAChC,UAAU,EAAE,GAAG,CAAC,kCAAkC;iBACnD;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAEtD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE;oBACR,cAAc;oBACd,UAAU,EAAE,CAAC;oBACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB;iBACpE;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAC7B,YAAoB,EACpB,MAAc,EACd,QAAgB,EAChB,MAAwB;QAExB,IAAI,CAAC;YACH,MAAM,GAAG,GAAkB;gBACzB,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,KAAK,EAAE,MAAM;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,mCAAmC,MAAM,CAAC,MAAM,EAAE;gBAC3D,IAAI,EAAE;oBACJ,YAAY;oBACZ,MAAM;oBACN,QAAQ;oBACR,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ;oBAC/C,KAAK,EAAE,iBAAiB;oBACxB,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU;oBACtC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC;oBAC3C,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK;iBAC7B;aACF,CAAC;YAEF,uDAAuD;YACvD,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;QAElD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,MAAc,EAAE,WAAmB;QACrD,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,wBAAwB;QAEhD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,YAAY;gBACf,OAAO,QAAQ,GAAG,GAAG,CAAC,CAAC,+BAA+B;YACxD,KAAK,YAAY;gBACf,OAAO,QAAQ,GAAG,GAAG,CAAC,CAAC,gCAAgC;YACzD,KAAK,cAAc;gBACjB,OAAO,QAAQ,GAAG,GAAG,CAAC,CAAC,6BAA6B;YACtD,KAAK,kBAAkB;gBACrB,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,yCAAyC;YAChE,KAAK,YAAY;gBACf,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,8BAA8B;YACrD;gBACE,OAAO,QAAQ,CAAC;QACpB,CAAC;IACH,CAAC;;AA5aH,gCA6aC;AA5ayB,yBAAc,GAAG,GAAG,CAAC,CAAC,+CAA+C"}