import * as XLSX from 'xlsx';
export declare class ExcelService {
    /**
     * Parse Excel file and convert to structured text
     */
    static parseExcel(fileBuffer: Buffer): Promise<string>;
    /**
     * Extract transaction data from Excel workbook
     */
    static extractTransactions(workbook: XLSX.WorkBook): any[];
    /**
     * Format Excel date to standard format
     */
    private static formatExcelDate;
    /**
     * Validate Excel structure for financial data
     */
    static validateFinancialExcel(workbook: XLSX.WorkBook): {
        isValid: boolean;
        errors: string[];
    };
    /**
     * Get sheet information
     */
    static getSheetInfo(workbook: XLSX.WorkBook): Array<{
        name: string;
        index: number;
        rowCount: number;
        columnCount: number;
        hasData: boolean;
    }>;
}
//# sourceMappingURL=excel-service.d.ts.map