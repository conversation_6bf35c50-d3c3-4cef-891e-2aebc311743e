"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const genai_1 = require("@google/genai");
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_MODEL_TEXT = 'gemini-2.5-flash';
const GEMINI_MODEL_MULTIMODAL = 'gemini-2.5-flash';
// Basic prompt optimized for speed and low token usage
const FINSCOPE_BASIC_PROMPT = `You are a financial data extraction specialist for Nigerian transaction receipts and bank statements.

REQUIRED FIELDS (must be extracted for every transaction):
- "date": The transaction date (YYYY-MM-DD). If the year is not present, infer it from surrounding context or assume the current year if context is insufficient.
- "time": The transaction time (HH:MM:SS), if available. If not, use null.
- "description": The full, original transaction description.
- "amount": A positive number representing the transaction value.
- "type": Must be either "credit" or "debit".
- "bank_provided_id": The official, unique ID for the transaction provided by the bank. This might be labeled 'Transaction ID', 'Reference No', 'Ref', 'Session ID', or similar. If you cannot find one, this field MUST be null.
- "source_bank": The name of the bank or financial institution (e.g., "GTBank", "Kuda", "Opay", "Access Bank", "Moniepoint", "First Bank"). If you cannot determine the bank, use "Unknown".
- "senderName": Name of the sender/account holder (if available, otherwise use null)
- "receiverName": Name of the recipient (if available, otherwise use null)

INSTRUCTIONS:
1. Extract ALL required fields for every transaction
2. Use null for missing optional fields (time, bank_provided_id, senderName, receiverName)
3. Return ONLY a JSON array with these exact field names
4. No additional text or explanations
5. Your entire response MUST be a single, valid, complete JSON array of objects
6. Do not include any other text, explanations, or markdown`;
// Advanced prompt with comprehensive extraction capabilities (based on index.tsx)
const FINSCOPE_ADVANCED_PROMPT = `You are a highly precise financial data extraction specialist for Nigerian bank statements and transaction receipts.
Your task is to analyze the provided text and extract comprehensive transaction details.

REQUIRED FIELDS (must be extracted for every transaction):
- "date": The transaction date (YYYY-MM-DD). If the year is not present, infer it from surrounding context or assume the current year if context is insufficient.
- "time": The transaction time (HH:MM:SS), if available. If not, use null.
- "description": The full, original transaction description.
- "amount": A positive number representing the transaction value.
- "type": Must be either "credit" or "debit".
- "bank_provided_id": The official, unique ID for the transaction provided by the bank. This might be labeled 'Transaction ID', 'Reference No', 'Ref', 'Session ID', or similar. If you cannot find one, this field MUST be null.
- "source_bank": The name of the bank or financial institution (e.g., "GTBank", "Kuda", "Opay", "Access Bank", "Moniepoint", "First Bank"). If you cannot determine the bank, use "Unknown".

OPTIONAL FIELDS (extract if available, use null if not found):
🧍‍♂️ Parties Involved:
- "senderName": Name of the sender/account holder
- "receiverName": Name of the recipient
- "senderAccountNumber": Sender's account number
- "receiverAccountNumber": Receiver's account number
- "accountType": Type of account (e.g., "Savings", "Current", "Business")
- "customerId": Customer ID or Profile ID

💰 Transaction Details:
- "transactionFee": Transaction fee or charges (decimal number)
- "currencyType": Currency type (e.g., "NGN", "USD", default to "NGN")
- "exchangeRate": Exchange rate if applicable (decimal number)
- "transactionStatus": Status (e.g., "Successful", "Failed", "Pending", default to "Successful")
- "paymentMethod": Payment method (e.g., "USSD", "Card", "Mobile App", "POS", "ATM")
- "narration": Additional narration or purpose
- "invoiceNumber": Invoice or bill number
- "merchantName": Merchant name if applicable
- "authorizationCode": Authorization code

🏦 Bank and Channel Info:
- "destinationBank": Destination bank name
- "bankBranch": Bank branch name or code
- "paymentGateway": Payment gateway or processor (e.g., "Flutterwave", "Paystack", "Interswitch")
- "terminalId": POS Terminal ID or ATM ID
- "channel": Channel of transaction (e.g., "Mobile App", "Web", "ATM", "POS", "USSD")

📅 Time and Identity:
- "sessionId": Session ID

📍 Location and Device Info:
- "deviceId": Device ID
- "ipAddress": IP Address
- "geolocation": Geolocation if captured

🏦 Account Standing:
- "balanceBefore": Balance before transaction (decimal number)
- "balanceAfter": Balance after transaction (decimal number)

INSTRUCTIONS:
1. Extract ALL required fields for every transaction
2. Extract optional fields ONLY if they are clearly present in the text
3. Use null for any optional field that is not found
4. Maintain the exact field names as specified above
5. Your entire response MUST be a single, valid, complete JSON array of objects
6. Do not include any other text, explanations, or markdown`;
// Legacy prompt for backward compatibility
const FINSCOPE_CATEGORIZATION_PROMPT = `You are a highly accurate financial transaction categorizer, specializing in Nigerian digital banking and fintech transactions.
---
🎯 TASK:
You will receive a JSON array of transactions. Your job is to analyze the \`description\` for each transaction and add a \`category\` field based on the list below.
---
🏷️ CATEGORIES (Use one of these exact strings):
**Income & Deposits:**
- \`Add Money (Wallet Top-Up)\`
- \`Bank Deposit\`
- \`Cash Deposit\`
- \`Interest Earned\`
- \`Investment Payback\`
- \`Refund\`
- \`Loan Disbursement\`
- \`Commission\`
**Bills & Utilities:**
- \`Utilities\`
- \`Airtime\`
- \`Data Bundle\`
- \`TV & Subscriptions\`
**Payments & Shopping:**
- \`Online Shopping\`
- \`Card Payment\`
- \`QR Code Payment\`
- \`Merchant Payment\`
- \`Business Payments\`
- \`Invoice Payments\`
**Savings & Loans:**
- \`Savings & Investments\`
- \`Loan Repayment\`
**Transfers:**
- \`Money Transfer\`
- \`Financial Institution Payment\`
**Travel & Transport:**
- \`Transportation & Tolls\`
- \`Travel & Hotel\`
**Life & Entertainment:**
- \`Education\`
- \`Events & Entertainment\`
- \`Betting\`
- \`Donations & Dues\`
**Government & Fees:**
- \`Government Payments\`
- \`Bank Charges\`
**Withdrawals:**
- \`Cash Withdrawal\`
**Other:**
- \`Other\`
---
📌 INSTRUCTIONS:
1.  Analyze the \`description\` using Nigerian banking context (e.g., POS, ATM, CashBox, Flutterwave, Bet9ja).
2.  Preserve ALL original fields from the input JSON (\`date\`, \`time\`, \`description\`, \`amount\`, \`type\`, \`bank_provided_id\`, \`source_bank\`).
3.  Add a new field, \`category\`, with the most appropriate value from the list.
4.  If no category clearly fits, use "Other".
5.  Your entire response must be ONLY the modified JSON array, starting with \`[\` and ending with \`]\`.
---`;
class AIService {
    static parseGeminiJsonResponse(responseText) {
        let jsonStr = responseText.trim();
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = jsonStr.match(fenceRegex);
        if (match && match[2]) {
            jsonStr = match[2].trim();
        }
        try {
            return JSON.parse(jsonStr);
        }
        catch (e) {
            console.error("Failed to parse JSON response:", jsonStr, e);
            throw new Error(`Invalid JSON response: ${e instanceof Error ? e.message : String(e)}. Raw text: ${responseText.substring(0, 1000)}`);
        }
    }
    static async makeResilientGeminiRequest(prompt, isJsonOutput = true, retries = 1) {
        try {
            const response = await this.ai.models.generateContent({
                model: GEMINI_MODEL_TEXT,
                contents: prompt,
                config: isJsonOutput ? { responseMimeType: "application/json" } : {}
            });
            const text = response.text;
            if (isJsonOutput) {
                return this.parseGeminiJsonResponse(text ?? '');
            }
            return text;
        }
        catch (error) {
            if (retries > 0) {
                console.warn(`Gemini request failed, retrying (${retries} left)... Error: ${error}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return this.makeResilientGeminiRequest(prompt, isJsonOutput, retries - 1);
            }
            console.error("Error in makeResilientGeminiRequest after retries:", error);
            throw error;
        }
    }
    static async makeResilientGeminiMultimodalRequest(promptParts, isJsonOutput = true, retries = 1) {
        try {
            const response = await this.ai.models.generateContent({
                model: GEMINI_MODEL_MULTIMODAL,
                contents: { parts: promptParts },
                config: isJsonOutput ? { responseMimeType: "application/json" } : {},
            });
            const text = response.text;
            if (isJsonOutput) {
                return this.parseGeminiJsonResponse(text ?? '');
            }
            return text;
        }
        catch (error) {
            if (retries > 0) {
                console.warn(`Gemini multimodal request failed, retrying (${retries} left)... Error: ${error}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return this.makeResilientGeminiMultimodalRequest(promptParts, isJsonOutput, retries - 1);
            }
            console.error("Error in makeResilientGeminiMultimodalRequest after retries:", error);
            throw error;
        }
    }
    static async analyzeText(text, analysisMode = 'advanced') {
        try {
            // Stage 1: Extraction - choose prompt based on analysis mode
            const selectedPrompt = analysisMode === 'basic' ? FINSCOPE_BASIC_PROMPT : FINSCOPE_ADVANCED_PROMPT;
            const extractionPrompt = selectedPrompt + "\n\nInput Text:\n" + text;
            const extractedData = await this.makeResilientGeminiRequest(extractionPrompt, true);
            if (!Array.isArray(extractedData)) {
                throw new Error("Extraction phase did not return a valid JSON array.");
            }
            let extractedTransactions = extractedData.map((item) => ({
                date: item.date || "N/A",
                time: item.time || null,
                description: item.description || "N/A",
                amount: typeof item.amount === 'number' ? item.amount : 0,
                type: item.type === "credit" || item.type === "debit" ? item.type : "debit",
                bank_provided_id: item.bank_provided_id || null,
                source_bank: item.source_bank || "Unknown",
                // Enhanced optional fields
                sender_name: item.senderName || null,
                receiver_name: item.receiverName || null,
                sender_account_number: item.senderAccountNumber || null,
                receiver_account_number: item.receiverAccountNumber || null,
                account_type: item.accountType || null,
                customer_id: item.customerId || null,
                transaction_fee: typeof item.transactionFee === 'number' ? item.transactionFee : null,
                currency_type: item.currencyType || 'NGN',
                exchange_rate: typeof item.exchangeRate === 'number' ? item.exchangeRate : null,
                transaction_status: item.transactionStatus || 'Successful',
                payment_method: item.paymentMethod || null,
                narration: item.narration || null,
                invoice_number: item.invoiceNumber || null,
                merchant_name: item.merchantName || null,
                authorization_code: item.authorizationCode || null,
                destination_bank: item.destinationBank || null,
                bank_branch: item.bankBranch || null,
                payment_gateway: item.paymentGateway || null,
                terminal_id: item.terminalId || null,
                channel: item.channel || null,
                session_id: item.sessionId || null,
                device_id: item.deviceId || null,
                ip_address: item.ipAddress || null,
                geolocation: item.geolocation || null,
                balance_before: typeof item.balanceBefore === 'number' ? item.balanceBefore : null,
                balance_after: typeof item.balanceAfter === 'number' ? item.balanceAfter : null
            }));
            const detectedBank = extractedTransactions.length > 0 && extractedTransactions[0]?.source_bank !== "Unknown"
                ? extractedTransactions[0]?.source_bank
                : "Unknown";
            // Stage 2: Categorization (skip for basic analysis to optimize speed)
            if (analysisMode === 'advanced') {
                const categorizationInputJson = JSON.stringify(extractedTransactions);
                const categorizationPrompt = FINSCOPE_CATEGORIZATION_PROMPT + "\n\nJSON to categorize:\n" + categorizationInputJson;
                const categorizedData = await this.makeResilientGeminiRequest(categorizationPrompt, true);
                if (!Array.isArray(categorizedData)) {
                    throw new Error("Categorization phase did not return a valid JSON array.");
                }
                // Merge categories
                if (categorizedData.length === extractedTransactions.length) {
                    extractedTransactions = extractedTransactions.map((tx, index) => ({
                        ...tx,
                        category: categorizedData[index]?.category || "Other",
                    }));
                }
                else {
                    extractedTransactions.forEach(tx => tx.category = "Other");
                }
            }
            else {
                // For basic analysis, set default category to optimize speed
                extractedTransactions.forEach(tx => tx.category = "Other");
            }
            // Map to Transaction type (with dummy user_id and bank_provided_id for now)
            const transactions = extractedTransactions.map((tx, index) => ({
                id: `temp_${Date.now()}_${index}`,
                user_id: "AI_USER", // Placeholder, should be set by controller
                date: tx.date,
                time: tx.time,
                description: tx.description,
                amount: tx.amount,
                type: tx.type,
                category: tx.category,
                bank_provided_id: tx.bank_provided_id || null,
                source_bank: tx.source_bank || "Unknown",
            }));
            return { transactions, detectedBank };
        }
        catch (error) {
            console.error('Error in AI analysis:', error);
            throw error;
        }
    }
    static async analyzeImage(imageBuffer, analysisMode = 'advanced') {
        try {
            // Convert buffer to base64
            const base64Image = imageBuffer.toString('base64');
            const mimeType = 'image/jpeg'; // You might want to detect this dynamically
            const imagePart = {
                inlineData: {
                    data: base64Image,
                    mimeType
                }
            };
            // Stage 1: Extraction - choose prompt based on analysis mode
            const selectedPrompt = analysisMode === 'basic' ? FINSCOPE_BASIC_PROMPT : FINSCOPE_ADVANCED_PROMPT;
            const multimodalPromptParts = [
                { text: selectedPrompt + "\n\nInput Image(s):\n" },
                imagePart
            ];
            const extractedData = await this.makeResilientGeminiMultimodalRequest(multimodalPromptParts, true);
            if (!Array.isArray(extractedData)) {
                throw new Error("Extraction phase did not return a valid JSON array.");
            }
            let extractedTransactions = extractedData.map((item) => ({
                date: item.date || "N/A",
                time: item.time || null,
                description: item.description || "N/A",
                amount: typeof item.amount === 'number' ? item.amount : 0,
                type: item.type === "credit" || item.type === "debit" ? item.type : "debit",
                bank_provided_id: item.bank_provided_id || null,
                source_bank: item.source_bank || "Unknown",
                // Enhanced optional fields
                sender_name: item.senderName || null,
                receiver_name: item.receiverName || null,
                sender_account_number: item.senderAccountNumber || null,
                receiver_account_number: item.receiverAccountNumber || null,
                account_type: item.accountType || null,
                customer_id: item.customerId || null,
                transaction_fee: typeof item.transactionFee === 'number' ? item.transactionFee : null,
                currency_type: item.currencyType || 'NGN',
                exchange_rate: typeof item.exchangeRate === 'number' ? item.exchangeRate : null,
                transaction_status: item.transactionStatus || 'Successful',
                payment_method: item.paymentMethod || null,
                narration: item.narration || null,
                invoice_number: item.invoiceNumber || null,
                merchant_name: item.merchantName || null,
                authorization_code: item.authorizationCode || null,
                destination_bank: item.destinationBank || null,
                bank_branch: item.bankBranch || null,
                payment_gateway: item.paymentGateway || null,
                terminal_id: item.terminalId || null,
                channel: item.channel || null,
                session_id: item.sessionId || null,
                device_id: item.deviceId || null,
                ip_address: item.ipAddress || null,
                geolocation: item.geolocation || null,
                balance_before: typeof item.balanceBefore === 'number' ? item.balanceBefore : null,
                balance_after: typeof item.balanceAfter === 'number' ? item.balanceAfter : null
            }));
            const detectedBank = extractedTransactions.length > 0 && extractedTransactions[0]?.source_bank !== "Unknown"
                ? extractedTransactions[0]?.source_bank
                : "Unknown";
            // Stage 2: Categorization (skip for basic analysis to optimize speed)
            if (analysisMode === 'advanced') {
                const categorizationInputJson = JSON.stringify(extractedTransactions);
                const categorizationPrompt = FINSCOPE_CATEGORIZATION_PROMPT + "\n\nJSON to categorize:\n" + categorizationInputJson;
                const categorizedData = await this.makeResilientGeminiRequest(categorizationPrompt, true);
                if (!Array.isArray(categorizedData)) {
                    throw new Error("Categorization phase did not return a valid JSON array.");
                }
                // Merge categories
                if (categorizedData.length === extractedTransactions.length) {
                    extractedTransactions = extractedTransactions.map((tx, index) => ({
                        ...tx,
                        category: categorizedData[index]?.category || "Other",
                    }));
                }
                else {
                    extractedTransactions.forEach(tx => tx.category = "Other");
                }
            }
            else {
                // For basic analysis, set default category to optimize speed
                extractedTransactions.forEach(tx => tx.category = "Other");
            }
            // Map to Transaction type (with dummy user_id and bank_provided_id for now)
            const transactions = extractedTransactions.map((tx, index) => ({
                id: `temp_${Date.now()}_${index}`,
                user_id: "AI_USER", // Placeholder, should be set by controller
                date: tx.date,
                time: tx.time,
                description: tx.description,
                amount: tx.amount,
                type: tx.type,
                category: tx.category,
                bank_provided_id: tx.bank_provided_id || null,
                source_bank: tx.source_bank || "Unknown",
            }));
            return { transactions, detectedBank };
        }
        catch (error) {
            console.error('Error in AI image analysis:', error);
            throw error;
        }
    }
}
exports.AIService = AIService;
AIService.ai = new genai_1.GoogleGenAI({ apiKey: GEMINI_API_KEY });
//# sourceMappingURL=ai.js.map