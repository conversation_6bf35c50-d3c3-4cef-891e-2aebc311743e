{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "baseUrl": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "paths": {"@shared/*": ["../../../src/types/*"]}}, "include": ["src/**/*", "../../../src/types/**/*"], "exclude": ["node_modules", "dist"]}