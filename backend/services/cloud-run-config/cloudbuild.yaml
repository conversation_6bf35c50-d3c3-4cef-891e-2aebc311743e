steps:
  # Build and deploy PDF Processor (Python)
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-f', 'cloud-run-config/Dockerfile.python',
      '-t', 'gcr.io/$PROJECT_ID/pdf-processor:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/pdf-processor:latest',
      '.'
    ]

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/pdf-processor:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/pdf-processor:latest']

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args: [
      'run', 'deploy', 'pdf-processor',
      '--image', 'gcr.io/$PROJECT_ID/pdf-processor:$COMMIT_SHA',
      '--platform', 'managed',
      '--region', 'us-central1',
      '--allow-unauthenticated',
      '--memory', '2Gi',
      '--cpu', '2',
      '--timeout', '900',
      '--max-instances', '10'
    ]

  # Build and deploy Document Processor (Node.js)
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-f', 'cloud-run-config/Dockerfile.node',
      '-t', 'gcr.io/$PROJECT_ID/document-processor:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/document-processor:latest',
      '.'
    ]

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/document-processor:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/document-processor:latest']

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args: [
      'run', 'deploy', 'document-processor',
      '--image', 'gcr.io/$PROJECT_ID/document-processor:$COMMIT_SHA',
      '--platform', 'managed',
      '--region', 'us-central1',
      '--allow-unauthenticated',
      '--memory', '1Gi',
      '--cpu', '1',
      '--timeout', '300',
      '--max-instances', '20',
      '--set-env-vars', 'PDF_SERVICE_URL=https://pdf-processor-$PROJECT_ID-uc.a.run.app',
      '--set-env-vars', 'GEMINI_API_KEY=$_GEMINI_API_KEY',
      '--set-env-vars', 'SUPABASE_URL=$_SUPABASE_URL',
      '--set-env-vars', 'SUPABASE_SERVICE_ROLE_KEY=$_SUPABASE_SERVICE_ROLE_KEY'
    ]

# Store images in Google Container Registry
images:
  - 'gcr.io/$PROJECT_ID/pdf-processor:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/pdf-processor:latest'
  - 'gcr.io/$PROJECT_ID/document-processor:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/document-processor:latest'

# Build timeout
timeout: '1800s'

# Substitution variables (set these in Cloud Build triggers)
substitutions:
  _GEMINI_API_KEY: ''
  _SUPABASE_URL: ''
  _SUPABASE_SERVICE_ROLE_KEY: '' 