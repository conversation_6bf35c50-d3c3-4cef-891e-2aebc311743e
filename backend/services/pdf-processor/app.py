import os
import io
import base64
import json
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime

import pdfplumber
import fitz  # PyMuPDF
from PIL import Image
import cv2
import numpy as np
from flask import Flask, request, jsonify, g, abort
from werkzeug.utils import secure_filename
from dotenv import load_dotenv
import requests
from jose import jwt, JWTError

# Import Supabase client
from supabase_client import get_supabase_client

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024  # 10MB file size limit

# Startup check for required Supabase env vars
required_supabase_env_vars = [
    "SUPABASE_URL",
    "SUPABASE_SERVICE_ROLE_KEY",
    "SUPABASE_ANON_KEY"
]
missing_vars = [var for var in required_supabase_env_vars if not os.getenv(var)]
if missing_vars:
    logger.error(f"Missing required Supabase environment variables: {', '.join(missing_vars)}")
    raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

# Initialize Supabase client
try:
    supabase = get_supabase_client()
    logger.info("Supabase client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Supabase client: {e}")
    raise

SUPABASE_URL = os.getenv("SUPABASE_URL")
if not SUPABASE_URL:
    logger.error("SUPABASE_URL environment variable is not set.")
    raise ValueError("SUPABASE_URL environment variable is required.")

SUPABASE_JWKS_URL = SUPABASE_URL + "/auth/v1/keys"
SUPABASE_ISSUER = SUPABASE_URL + "/auth/v1"
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY")
SUPABASE_PUBLISHABLE_KEY = os.getenv("SUPABASE_PUBLISHABLE_KEY")
SUPABASE_SECRET_KEY = os.getenv("SUPABASE_SECRET_KEY")

def get_jwk_set():
    headers = {"apikey": SUPABASE_PUBLISHABLE_KEY}
    response = requests.get(SUPABASE_JWKS_URL, headers=headers)
    response.raise_for_status()
    return response.json()

def verify_supabase_jwt(token):
    jwks = get_jwk_set()
    try:
        payload = jwt.decode(
            token,
            jwks,
            algorithms=["ES256", "HS256"],  # Accept both during transition
            issuer=SUPABASE_ISSUER,
            options={"verify_aud": False}
        )
        return payload
    except JWTError as e:
        raise Exception(f"Invalid JWT: {e}")

@app.before_request
def authenticate():
    if request.endpoint == 'health_check':
        return
    auth_header = request.headers.get("Authorization", "")
    if not auth_header.startswith("Bearer "):
        abort(401, "Missing or invalid Authorization header")
    token = auth_header.split(" ", 1)[1]
    try:
        user_claims = verify_supabase_jwt(token)
        g.user = user_claims
    except Exception as e:
        abort(401, str(e))

class PDFProcessor:
    """Handles PDF text extraction and image conversion"""
    
    @staticmethod
    def has_extractable_text(pdf_buffer: bytes) -> Tuple[bool, str]:
        """
        Check if PDF has extractable text and return the text if available
        
        Returns:
            Tuple[bool, str]: (has_text, extracted_text)
        """
        try:
            with pdfplumber.open(io.BytesIO(pdf_buffer)) as pdf:
                extracted_text = ""
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        extracted_text += text + "\n"
                
                # Check if we have substantial text (more than 100 characters)
                has_text = len(extracted_text.strip()) > 100
                return has_text, extracted_text.strip()
                
        except Exception as e:
            logger.error(f"Error checking PDF text: {e}")
            return False, ""
    
    @staticmethod
    def extract_text_with_pdfplumber(pdf_buffer: bytes) -> str:
        """Extract text from PDF using pdfplumber"""
        try:
            with pdfplumber.open(io.BytesIO(pdf_buffer)) as pdf:
                text_parts = []
                
                for page_num, page in enumerate(pdf.pages):
                    # Extract text
                    text = page.extract_text()
                    if text:
                        text_parts.append(f"--- Page {page_num + 1} ---\n{text}")
                    
                    # Extract tables if present
                    tables = page.extract_tables()
                    if tables:
                        for table_num, table in enumerate(tables):
                            table_text = "\n".join(["\t".join([str(cell) if cell else "" for cell in row]) for row in table])
                            text_parts.append(f"--- Table {table_num + 1} on Page {page_num + 1} ---\n{table_text}")
                
                return "\n\n".join(text_parts)
                
        except Exception as e:
            logger.error(f"Error extracting text with pdfplumber: {e}")
            raise
    
    @staticmethod
    def convert_pdf_to_images(pdf_buffer: bytes, dpi: int = 300) -> List[Dict]:
        """
        Convert PDF pages to images for OCR processing
        
        Returns:
            List[Dict]: List of image data with metadata
        """
        try:
            # Open PDF with PyMuPDF
            pdf_document = fitz.open(stream=pdf_buffer, filetype="pdf")
            images = []
            
            for page_num in range(len(pdf_document)):
                page = pdf_document.load_page(page_num)
                
                # Set zoom factor for higher resolution
                zoom = dpi / 72.0
                mat = fitz.Matrix(zoom, zoom)
                
                # Render page to image
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # Convert to PIL Image for processing
                img = Image.open(io.BytesIO(img_data))
                
                # Optimize image for OCR
                img_optimized = PDFProcessor.optimize_image_for_ocr(img)
                
                # Convert back to bytes
                img_buffer = io.BytesIO()
                img_optimized.save(img_buffer, format='PNG', optimize=True)
                img_bytes = img_buffer.getvalue()
                
                images.append({
                    "page_number": page_num + 1,
                    "image_data": base64.b64encode(img_bytes).decode('utf-8'),
                    "mime_type": "image/png",
                    "width": img_optimized.width,
                    "height": img_optimized.height,
                    "size_bytes": len(img_bytes)
                })
            
            pdf_document.close()
            return images
            
        except Exception as e:
            logger.error(f"Error converting PDF to images: {e}")
            raise
    
    @staticmethod
    def optimize_image_for_ocr(img: Image.Image) -> Image.Image:
        """Optimize image for better OCR results"""
        try:
            # Convert to numpy array
            img_array = np.array(img)
            
            # Convert to grayscale if not already
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # Apply image enhancement for better OCR
            # 1. Denoise
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # 2. Increase contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # 3. Sharpen
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced, -1, kernel)
            
            # Convert back to PIL Image
            return Image.fromarray(sharpened)
            
        except Exception as e:
            logger.error(f"Error optimizing image: {e}")
            return img  # Return original if optimization fails

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "success": True,
        "message": "PDF Processor Service is running",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    })

@app.route('/api/v1/process', methods=['POST'])
def process_pdf():
    """Process PDF and extract text or convert to images"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            logger.error("No file provided in request.")
            return jsonify({
                "success": False,
                "error": "No file provided"
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            logger.error("No file selected in request.")
            return jsonify({
                "success": False,
                "error": "No file selected"
            }), 400
        
        # Use user ID from JWT
        user_id = g.user['sub']
        
        # Read file buffer
        file_buffer = file.read()
        filename = secure_filename(file.filename)
        
        logger.info(f"Processing PDF: {filename} for user: {user_id}")
        
        # Check if it's a PDF
        if not filename.lower().endswith('.pdf'):
            logger.error(f"File extension not PDF: {filename}")
            return jsonify({
                "success": False,
                "error": "File must be a PDF"
            }), 400
        
        # Check if PDF has extractable text
        has_text, extracted_text = PDFProcessor.has_extractable_text(file_buffer)
        
        if has_text:
            # Use pdfplumber for text extraction
            logger.info(f"PDF has extractable text, using pdfplumber for file: {filename}")
            
            try:
                full_text = PDFProcessor.extract_text_with_pdfplumber(file_buffer)
                
                # Log processing
                log_processing(user_id, filename, "text_extraction", True, {
                    "method": "pdfplumber",
                    "text_length": len(full_text),
                    "pages_processed": len(full_text.split("--- Page"))
                })
                
                return jsonify({
                    "success": True,
                    "data": {
                        "extraction_method": "pdfplumber",
                        "text": full_text,
                        "has_text": True,
                        "filename": filename,
                        "user_id": user_id,
                        "processed_at": datetime.utcnow().isoformat()
                    },
                    "message": "PDF text extracted successfully using pdfplumber"
                })
                
            except Exception as e:
                logger.error(f"Error extracting text with pdfplumber for file {filename}: {e}")
                # Fall back to image conversion
                has_text = False
        if not has_text:
            # Convert PDF to images for OCR
            logger.info(f"PDF has no extractable text, converting to images for OCR for file: {filename}")
            try:
                images = PDFProcessor.convert_pdf_to_images(file_buffer)
                # Log processing
                log_processing(user_id, filename, "image_conversion", True, {
                    "method": "pdf_to_image",
                    "pages_converted": len(images),
                    "total_size_bytes": sum(img["size_bytes"] for img in images)
                })
                return jsonify({
                    "success": True,
                    "data": {
                        "extraction_method": "image_conversion",
                        "images": images,
                        "has_text": False,
                        "filename": filename,
                        "user_id": user_id,
                        "processed_at": datetime.utcnow().isoformat()
                    },
                    "message": f"PDF converted to {len(images)} images for OCR processing"
                })
            except Exception as e:
                logger.error(f"Error converting PDF to images for file {filename}: {e}")
                # Log error
                log_processing(user_id, filename, "image_conversion", False, {
                    "error": str(e)
                })
                return jsonify({
                    "success": False,
                    "error": f"Failed to process PDF: {str(e)}"
                }), 500
    except Exception as e:
        logger.error(f"Unexpected error processing PDF: {e}")
        return jsonify({
            "success": False,
            "error": "Internal server error"
        }), 500

@app.route('/api/v1/batch', methods=['POST'])
def process_pdf_batch():
    """Process multiple PDFs in batch"""
    try:
        # Check if files were uploaded
        if 'files' not in request.files:
            logger.error("No files provided in batch request.")
            return jsonify({
                "success": False,
                "error": "No files provided"
            }), 400
        files = request.files.getlist('files')
        if not files or files[0].filename == '':
            logger.error("No files selected in batch request.")
            return jsonify({
                "success": False,
                "error": "No files selected"
            }), 400
        # Use user ID from JWT
        user_id = g.user['sub']
        batch_id = request.form.get('batchId') or f"batch_{datetime.utcnow().timestamp()}"
        logger.info(f"Processing batch: {batch_id} with {len(files)} PDFs")
        results = []
        success_count = 0
        error_count = 0
        for file in files:
            try:
                file_buffer = file.read()
                filename = secure_filename(file.filename)
                # Check if it's a PDF
                if not filename.lower().endswith('.pdf'):
                    logger.error(f"File extension not PDF in batch: {filename}")
                    results.append({
                        "filename": filename,
                        "success": False,
                        "error": "File must be a PDF"
                    })
                    error_count += 1
                    continue
                # Process the PDF
                has_text, extracted_text = PDFProcessor.has_extractable_text(file_buffer)
                if has_text:
                    full_text = PDFProcessor.extract_text_with_pdfplumber(file_buffer)
                    results.append({
                        "filename": filename,
                        "success": True,
                        "extraction_method": "pdfplumber",
                        "text": full_text,
                        "has_text": True
                    })
                    success_count += 1
                else:
                    images = PDFProcessor.convert_pdf_to_images(file_buffer)
                    results.append({
                        "filename": filename,
                        "success": True,
                        "extraction_method": "image_conversion",
                        "images": images,
                        "has_text": False
                    })
                    success_count += 1
            except Exception as e:
                logger.error(f"Error processing {file.filename} in batch {batch_id}: {e}")
                results.append({
                    "filename": file.filename,
                    "success": False,
                    "error": str(e)
                })
                error_count += 1
        # Log batch processing
        log_processing(user_id, f"batch_{batch_id}", "batch_processing", True, {
            "batch_id": batch_id,
            "total_files": len(files),
            "success_count": success_count,
            "error_count": error_count
        })
        return jsonify({
            "success": True,
            "data": {
                "batch_id": batch_id,
                "total_files": len(files),
                "success_count": success_count,
                "error_count": error_count,
                "results": results
            },
            "message": f"Batch processing completed: {success_count} successful, {error_count} failed"
        })
    except Exception as e:
        logger.error(f"Unexpected error in batch processing: {e}")
        return jsonify({
            "success": False,
            "error": "Internal server error"
        }), 500

# Development-only logs endpoint
@app.route('/logs', methods=['GET'])
def get_logs():
    if os.getenv('FLASK_ENV') != 'development':
        return jsonify({"error": "Logs endpoint only available in development mode."}), 403
    try:
        limit = int(request.args.get('limit', 20))
        # Get logs from Supabase processing_logs table
        response = supabase._make_request(
            'GET',
            f'processing_logs?order=timestamp.desc&limit={limit}'
        )
        logs = response.json()
        return jsonify({"logs": logs})
    except Exception as e:
        logger.error(f"Error fetching logs: {e}")
        return jsonify({"error": "Failed to fetch logs."}), 500

def log_processing(user_id: str, filename: str, operation: str, success: bool, metadata: Dict):
    """Log processing operations to Supabase"""
    try:
        supabase.log_processing(user_id, filename, operation, success, metadata)
    except Exception as e:
        logger.error(f"Error logging to Supabase: {e}")

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False) 