import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import multer from 'multer';
import dotenv from 'dotenv';

// Import middleware and controllers
import { authenticateUser } from './middleware/auth';
import { AnalysisController } from './controllers/analysis';
import { TransactionsController } from './controllers/transactions';
import { DocumentAnalysisController } from './controllers/document-analysis';
import { LoggingController } from './controllers/logging';
import { initializeSupabase } from './config/supabase';
import { initializeSupabase as initializeDocumentProcessorSupabase } from '../services/document-processor/src/supabase';

// Load environment variables
dotenv.config({ path: '.env' });

// Initialize Supabase
try {
  initializeSupabase();
  console.log('✅ Supabase initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Supabase:', error);
  process.exit(1);
}

// Initialize Document Processor Supabase
try {
  initializeDocumentProcessorSupabase();
  console.log('✅ Document Processor Supabase initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Document Processor Supabase:', error);
  process.exit(1);
}

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (_req, file, cb) => {
    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png',
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'text/csv',
      'application/vnd.ms-excel' // .csv legacy
    ];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, PDF, XLSX, and CSV are allowed.'));
    }
  }
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({
    success: true,
    message: 'FinScope API is running',
    timestamp: new Date().toISOString()
  });
});

// API Routes
const apiRouter = express.Router();

// Analysis routes
apiRouter.post('/analyze/text', authenticateUser, AnalysisController.analyzeText);
apiRouter.post('/analyze/image', authenticateUser, upload.single('image'), AnalysisController.analyzeImage);

// Document processing routes
apiRouter.post('/documents/process', authenticateUser, upload.single('document'), DocumentAnalysisController.processDocument);
apiRouter.get('/documents/history', authenticateUser, DocumentAnalysisController.getProcessingHistory);
apiRouter.post('/documents/validate', authenticateUser, DocumentAnalysisController.validateExtractedData);
apiRouter.get('/documents/stats', authenticateUser, DocumentAnalysisController.getProcessingStats);

// File routing test endpoint (for development/testing)
apiRouter.post('/documents/route-test', authenticateUser, upload.single('document'), DocumentAnalysisController.testFileRouting);

// Transaction routes
apiRouter.get('/transactions', authenticateUser, TransactionsController.getTransactions);
apiRouter.put('/transactions/:id', authenticateUser, TransactionsController.updateTransaction);
apiRouter.delete('/transactions/:id', authenticateUser, TransactionsController.deleteTransaction);
apiRouter.get('/transactions/stats', authenticateUser, TransactionsController.getTransactionStats);
apiRouter.post('/transactions/autocategorize', authenticateUser, TransactionsController.autoCategorize);

// Logging routes
apiRouter.post('/error-logs', authenticateUser, LoggingController.createErrorLog);
apiRouter.get('/error-logs', authenticateUser, LoggingController.getErrorLogs);
apiRouter.post('/performance-logs', authenticateUser, LoggingController.createPerformanceLog);
apiRouter.get('/performance-logs', authenticateUser, LoggingController.getPerformanceLogs);

// Mount API routes
app.use('/api/v1', apiRouter);

// Error handling middleware
app.use((error: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error('Unhandled error:', error);
  
  if (error instanceof multer.MulterError) {
    return res.status(400).json({
      success: false,
      error: 'File upload error: ' + error.message
    });
  }

  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use('*', (_req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 FinScope API server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API base URL: http://localhost:${PORT}/api/v1`);
});

export default app; 