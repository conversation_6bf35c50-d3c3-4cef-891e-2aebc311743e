import { getSupabaseServiceClient } from '../config/supabase';
import { AuthRequest, ApiResponse } from '../../services/document-processor/src/types';

export interface ErrorLog {
  id?: string;
  error_name: string;
  error_message: string;
  error_stack?: string;
  context?: any;
  user_id?: string;
  request_id?: string;
  timestamp: string;
  created_at?: string;
}

export interface PerformanceLog {
  id?: string;
  operation: string;
  duration: number;
  metadata?: any;
  user_id?: string;
  timestamp: string;
  created_at?: string;
}

export class LoggingController {
  // Create error log
  static async createErrorLog(req: AuthRequest, res: any) {
    try {
      const userId = req.user!.uid;
      const errorData = req.body as Omit<ErrorLog, 'id' | 'created_at'>;

      // Ensure user_id is set to the authenticated user
      const logEntry = {
        ...errorData,
        user_id: userId,
        timestamp: errorData.timestamp || new Date().toISOString(),
        created_at: new Date().toISOString()
      };

      const { data, error } = await getSupabaseServiceClient()
        .from('error_logs')
        .insert([logEntry])
        .select()
        .single();

      if (error) {
        console.error('Error creating error log:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to create error log'
        });
      }

      const response: ApiResponse<ErrorLog> = {
        success: true,
        data,
        message: 'Error log created successfully'
      };

      res.json(response);
    } catch (error) {
      console.error('Create error log error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create error log'
      });
    }
  }

  // Get error logs
  static async getErrorLogs(req: AuthRequest, res: any) {
    try {
      const userId = req.user!.uid;
      const { limit = '50' } = req.query as { limit?: string };

      const { data, error } = await getSupabaseServiceClient()
        .from('error_logs')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false })
        .limit(parseInt(limit));

      if (error) {
        console.error('Error fetching error logs:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch error logs'
        });
      }

      const response: ApiResponse<ErrorLog[]> = {
        success: true,
        data: data || [],
        message: `Retrieved ${data?.length || 0} error logs`
      };

      res.json(response);
    } catch (error) {
      console.error('Get error logs error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch error logs'
      });
    }
  }

  // Create performance log
  static async createPerformanceLog(req: AuthRequest, res: any) {
    try {
      const userId = req.user!.uid;
      const perfData = req.body as Omit<PerformanceLog, 'id' | 'created_at'>;

      // Ensure user_id is set to the authenticated user
      const logEntry = {
        ...perfData,
        user_id: userId,
        timestamp: perfData.timestamp || new Date().toISOString(),
        created_at: new Date().toISOString()
      };

      const { data, error } = await getSupabaseServiceClient()
        .from('performance_logs')
        .insert([logEntry])
        .select()
        .single();

      if (error) {
        console.error('Error creating performance log:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to create performance log'
        });
      }

      const response: ApiResponse<PerformanceLog> = {
        success: true,
        data,
        message: 'Performance log created successfully'
      };

      res.json(response);
    } catch (error) {
      console.error('Create performance log error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create performance log'
      });
    }
  }

  // Get performance logs
  static async getPerformanceLogs(req: AuthRequest, res: any) {
    try {
      const userId = req.user!.uid;
      const { operation, limit = '100' } = req.query as { operation?: string; limit?: string };

      let query = getSupabaseServiceClient()
        .from('performance_logs')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false })
        .limit(parseInt(limit));

      if (operation) {
        query = query.eq('operation', operation);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching performance logs:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch performance logs'
        });
      }

      const response: ApiResponse<PerformanceLog[]> = {
        success: true,
        data: data || [],
        message: `Retrieved ${data?.length || 0} performance logs`
      };

      res.json(response);
    } catch (error) {
      console.error('Get performance logs error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch performance logs'
      });
    }
  }
}
