body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    margin: 0;
    background-color: #f4f7f6;
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

h1, h2, h3 {
    color: #2c3e50;
}

header {
    background-color: #3498db;
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
    margin: 0;
    font-size: 1.5rem;
    color: white;
}

#sign-out-button {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

#sign-out-button:hover {
    background-color: #c0392b;
}

#screen-auth {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
    flex-grow: 1;
}

#google-signin-button {
    background-color: #4285F4;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#google-signin-button:hover {
    background-color: #357ae8;
}

#main-app-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden; /* Prevents double scrollbars if content is too long */
}

#app {
    padding: 1.5rem;
    background-color: #fff;
    flex-grow: 1;
    overflow-y: auto; /* Allows content within screens to scroll */
}

.app-screen h2 {
    margin-top: 0;
    color: #3498db;
}

/* Upload Screen Styles */
#screen-upload .upload-area {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

#screen-upload .upload-option {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

#screen-upload label {
    font-weight: bold;
    color: #34495e;
}

#screen-upload input[type="file"],
#screen-upload textarea {
    padding: 0.75rem;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
}

#screen-upload input[type="file"] {
    cursor: pointer;
}

#screen-upload small {
    font-size: 0.85rem;
    color: #7f8c8d;
}

#screen-upload .separator-text {
    text-align: center;
    font-weight: bold;
    color: #7f8c8d;
    margin: 0.5rem 0;
}

#analyze-button {
    background-color: #2ecc71;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

#analyze-button:hover {
    background-color: #27ae60;
}

#analyze-button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

#analysis-status {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

#analysis-status.processing {
    background-color: #eaf2f8;
    border: 1px solid #aed6f1;
    color: #2980b9;
}

#analysis-status.success {
    background-color: #e8f8f5;
    border: 1px solid #a3e4d7;
    color: #1abc9c;
}

#analysis-status.error {
    background-color: #fdedec;
    border: 1px solid #fadbd8;
    color: #e74c3c;
}


#bottom-nav-bar {
    display: flex;
    justify-content: space-around;
    background-color: #ffffff;
    padding: 0.75rem 0;
    border-top: 1px solid #e0e0e0;
    box-shadow: 0 -2px 5px rgba(0,0,0,0.05);
}

.nav-button {
    background: none;
    border: none;
    color: #7f8c8d;
    padding: 0.5rem 1rem;
    cursor: pointer;
    font-size: 0.9rem;
    text-align: center;
    flex-grow: 1;
}

.nav-button.active {
    color: #3498db;
    font-weight: bold;
}

.nav-button:hover:not(.active) {
    color: #34495e;
}

.hidden {
    display: none !important;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
}

.modal-content {
    background-color: #fff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    width: 90%;
    max-width: 500px;
    text-align: left;
}

.modal-content h3 {
    margin-top: 0;
    color: #3498db;
}

.modal-content .close-modal-button {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 1rem;
    float: right;
}

.modal-content .close-modal-button:hover {
    background-color: #c0392b;
}

/* Accessibility focus outline */
button:focus, [role="button"]:focus, input:focus, select:focus, textarea:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}