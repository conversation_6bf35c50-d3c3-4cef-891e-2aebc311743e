# FinScope Document Processing Error Fixes

## Problem Summary
Multiple cascading errors when processing PDF files through the frontend upload interface:
1. **403 Forbidden**: Supabase RLS permission errors on logging tables
2. **500 Internal Server Error**: Backend document processing API failures
3. **503 Service Unavailable**: Document processor service unavailable
4. **400 Bad Request**: Performance logging issues

## Root Causes & Solutions

### 1. Supabase RLS Permission Errors (403 Forbidden)

**Root Cause**: Missing RLS policies for authenticated users on logging tables:
- `processing_logs`: Only SELECT policy, no INSERT/UPDATE for users
- `error_logs`: Only service role policy, no user policies  
- `performance_logs`: Only service role policy, no user policies

**Solution**: 
1. Run the SQL script: `/home/<USER>/VS Code/FinScope/supabase/fix_logging_rls_policies.sql`
2. This adds INSERT/UPDATE policies for authenticated users on all logging tables
3. Copy and paste the entire script into your Supabase SQL Editor and execute

### 2. Service Unavailable Error (503)

**Root Cause**: Document processor service failed to start due to port 8080 already in use.

**Solution**: 
```bash
# Kill process using port 8080
lsof -ti:8080 | xargs kill -9

# Restart document processor service
cd backend/services/document-processor
npm start
```

**Status**: ✅ Fixed - Service now running on port 8080

### 3. Backend API Error (500)

**Root Cause**: Document processing API fails when document processor service is unavailable (503 error propagates up).

**Solution**: Fixed by resolving the 503 error above. The FileTypeRouter routes files to:
- PDF files → PDF processor service
- Other files → Document processor service

### 4. Performance Logging Issues (400 Bad Request)

**Root Cause**: Same as #1 - missing RLS policies prevent frontend from writing to `performance_logs` table.

**Solution**: Fixed by the RLS policy updates in solution #1.

## Implementation Steps

### Step 1: Fix Supabase RLS Policies
1. Open your Supabase dashboard
2. Go to SQL Editor
3. Copy and paste the entire content of `supabase/fix_logging_rls_policies.sql`
4. Execute the script
5. Verify policies were created using the SELECT query at the end

### Step 2: Restart Document Processor Service
```bash
# From project root
cd backend/services/document-processor
npm start
```

### Step 3: Test Document Processing
1. Upload a PDF file through the frontend
2. Check browser Network tab - should see successful API calls
3. Verify no 403/500/503 errors in console

## Verification

After implementing these fixes:
- ✅ Frontend can write to logging tables (no more 403 errors)
- ✅ Document processor service is running (no more 503 errors)  
- ✅ Backend API processes documents successfully (no more 500 errors)
- ✅ Performance logging works (no more 400 errors)

## Architecture Notes

**Frontend Logging**: The frontend Supabase client uses authenticated user tokens and requires RLS policies that allow INSERT operations for the user's own records.

**Backend Services**: The backend uses service role keys and can bypass RLS, but the document processor service must be running for file processing to work.

**Service Dependencies**: 
- Main backend API (port 3001) → Document processor service (port 8080)
- Document processor service → Supabase (with service role)
- Frontend → Supabase (with user auth tokens + RLS policies)
